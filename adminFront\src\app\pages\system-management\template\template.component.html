<nb-card accent="success">
  <nb-card-header>
    <ngx-breadcrumb></ngx-breadcrumb>
  </nb-card-header>
  <nb-card-body>
    <div class="alert alert-info mb-4"
      style="border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;">
      <div class="d-flex align-items-center">
        <i class="fas fa-info-circle text-primary me-3" style="font-size: 1.2rem;"></i>
        <div>
          <p class="mb-0 text-muted" style="font-size: 0.9rem;">
            在此頁面您可以管理系統中的各個模板資訊，包括新增、編輯、刪除模板，以及設定模板名稱、狀態和包含的空間等。
          </p>
        </div>
      </div>
    </div>

    <!-- 搜尋條件區域 -->
    <div class="d-flex flex-wrap">
      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="templateName" class="label col-3">模板名稱</label>
          <nb-form-field class="col-9">
            <input type="text" id="templateName" nbInput class="w-full" placeholder="搜尋模板名稱..."
              [(ngModel)]="searchKeyword" (keyup.enter)="onSearch()">
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="status" class="label col-3">狀態</label>
          <nb-form-field class="col-9">
            <nb-select id="status" placeholder="選擇狀態..." [(ngModel)]="searchStatus" (selectedChange)="onSearch()">
              <nb-option [value]="null">全部</nb-option>
              <nb-option [value]="1">啟用</nb-option>
              <nb-option [value]="0">停用</nb-option>
            </nb-select>
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <div class="form-group d-flex align-items-center w-full">
          <label for="templateType" class="label col-3">模板類型</label>
          <nb-form-field class="col-9">
            <nb-select id="templateType" placeholder="選擇模板類型..." [(ngModel)]="searchTemplateType"
              (selectedChange)="onSearch()">
              <nb-option [value]="null">全部</nb-option>
              <nb-option [value]="1">空間模板</nb-option>
              <nb-option [value]="2">項目模板</nb-option>
            </nb-select>
          </nb-form-field>
        </div>
      </div>

      <div class="col-md-6">
        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->
      </div>

      <!-- 查詢和重置按鈕 -->
      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full mt-2 mb-3">
          <button class="btn btn-outline-secondary btn-sm me-2" (click)="onReset()">
            <i class="fas fa-undo me-1"></i>重置
          </button>
          <button class="btn btn-secondary btn-sm" (click)="onSearch()">
            <i class="fas fa-search me-1"></i>查詢
          </button>
        </div>
      </div>

      <div class="col-md-12">
        <div class="d-flex justify-content-end w-full mt-3">
          <button class="btn btn-info mx-1 btn-sm" *ngIf="isCreate" (click)="openCreateModal(templateModal)">
            <i class="fas fa-plus me-1"></i>新增模板
          </button>
        </div>
      </div>
    </div>

    <!-- 模板列表表格 -->
    <div class="table-responsive mt-4">
      <table class="table" style="min-width: 800px;">
        <thead>
          <tr>
            <th scope="col" style="width: 120px;">模板類型</th>
            <th scope="col" style="width: 200px;">模板名稱</th>
            <th scope="col" style="width: 100px;">狀態</th>
            <th scope="col" style="width: 180px;">建立時間</th>
            <th scope="col" style="width: 120px;">建立者</th>
            <th scope="col" style="width: 140px;">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let template of templateList">
            <td>
              {{ template.CTemplateType === 1 ? '空間模板' : (template.CTemplateType === 2 ? '項目模板' : '-') }}
            </td>
            <td>{{ template.CTemplateName }}</td>
            <td>
              {{ template.CStatus === 1 ? '啟用' : '停用' }}
            </td>
            <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>
            <td>{{ template.CCreator || '-' }}</td>
            <td class="table-actions">
              <button class="btn btn-outline-info btn-sm me-1"
                (click)="viewTemplateDetail(template, templateDetailModal)">
                <i class="fas fa-eye"></i>查看
              </button>
              <button *ngIf="isUpdate" class="btn btn-outline-warning btn-sm me-1"
                (click)="openEditModal(templateModal, template)">
                <i class="fas fa-edit"></i>編輯
              </button>
              <button *ngIf="isDelete" class="btn btn-outline-danger btn-sm" (click)="deleteTemplate(template)">
                <i class="fas fa-trash"></i>刪除
              </button>
            </td>
          </tr>
          <tr *ngIf="templateList.length === 0">
            <td colspan="5" class="text-muted py-4">
              <i class="fas fa-info-circle me-2"></i>目前沒有任何模板
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </nb-card-body>
  <nb-card-footer class="d-flex justify-content-center">
    <ngx-pagination [(Page)]="pageIndex" [PageSize]="pageSize" [CollectionSize]="totalRecords"
      (PageChange)="pageChanged($event)">
    </ngx-pagination>
  </nb-card-footer>
</nb-card>

<!-- 模板模態框（新增/編輯） -->
<ng-template #templateModal let-ref="dialogRef">
  <nb-card [style.width]="modalWidth"
    style="max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
    <nb-card-header class="d-flex justify-content-between align-items-center border-bottom py-3 px-4">
      <h5 class="mb-0 text-primary font-weight-bold">
        <i class="fas me-2" [class.fa-plus-circle]="!isEditMode" [class.fa-edit]="isEditMode"
          [class.text-success]="!isEditMode" [class.text-warning]="isEditMode"></i>
        {{ isEditMode ? '編輯模板' : '新增模板' }}
      </h5>
      <button type="button" class="btn btn-ghost-light btn-sm rounded-circle" (click)="onClose(ref)"
        style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
        <i class="fas fa-times"></i>
      </button>
    </nb-card-header>

    <nb-card-body class="px-4 py-4">
      <div class="row">
        <!-- 模板名稱、狀態和類型 -->
        <div class="col-12">
          <div class="form-group mb-4">
            <div class="row mb-4">
              <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                  <label for="templateName" class="required-field mb-0"
                    style="min-width: 85px; font-weight: 500; padding-top: 8px;">模板名稱</label>
                  <div class="flex-grow-1 ms-2">
                    <input type="text" id="templateName" class="form-control" nbInput placeholder="請輸入模板名稱"
                      [(ngModel)]="templateDetail.CTemplateName" name="templateName"
                      (keydown.control.enter)="onSubmit(ref)"
                      style="height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;" />
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="d-flex align-items-center mb-3">
                  <label for="templateStatus" class="required-field mb-0"
                    style="min-width: 85px; font-weight: 500; padding-top: 8px;">狀態</label>
                  <div class="flex-grow-1 ms-2">
                    <nb-form-field class="w-full">
                      <nb-select id="templateStatus" [(ngModel)]="templateDetail.CStatus" name="templateStatus"
                        placeholder="選擇狀態" style="height: 42px;">
                        <nb-option [value]="1">
                          <span class="d-flex align-items-center">
                            <i class="fas fa-check-circle text-success me-2"></i>啟用
                          </span>
                        </nb-option>
                        <nb-option [value]="0">
                          <span class="d-flex align-items-center">
                            <i class="fas fa-times-circle text-danger me-2"></i>停用
                          </span>
                        </nb-option>
                      </nb-select>
                    </nb-form-field>
                  </div>
                </div>
              </div>
            </div>

            <!-- 模板類型 -->
            <div class="row mb-4">
              <div class="col-md-6">
                <div class="d-flex align-items-center">
                  <label for="templateType" class="required-field mb-0"
                    style="min-width: 85px; font-weight: 500; padding-top: 8px;">模板類型</label>
                  <div class="flex-grow-1 ms-2">
                    <!-- 編輯模式：顯示當前類型，不可更改 -->
                    <div *ngIf="isEditMode" class="form-control d-flex align-items-center"
                      style="height: 42px; border-radius: 6px; background-color: #f8f9fa;">
                      <i class="fas fa-info-circle text-info me-2"></i>
                      <span>{{ templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate ? '空間模板' : '項目模板'
                        }}</span>
                    </div>
                    <!-- 新增模式：可選擇類型 -->
                    <nb-form-field *ngIf="!isEditMode" class="w-full">
                      <nb-select id="templateType" [(ngModel)]="templateDetail.CTemplateType" name="templateType"
                        placeholder="選擇類型" style="height: 42px;">
                        <nb-option [value]="EnumTemplateType.SpaceTemplate">
                          <span class="d-flex align-items-center">
                            <i class="fas fa-home text-primary me-2"></i>空間模板
                          </span>
                        </nb-option>
                        <nb-option [value]="EnumTemplateType.ItemTemplate">
                          <span class="d-flex align-items-center">
                            <i class="fas fa-list text-success me-2"></i>項目模板
                          </span>
                        </nb-option>
                      </nb-select>
                    </nb-form-field>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <!-- 空白區域，保持對齊 -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 明細項目選擇區域 -->
      <div class="col-12">
        <div class="form-group mb-4">
          <div class="row">
            <div class="col-md-12 d-flex align-items-start">
              <label class="required-field mb-0"
                style="min-width: 85px; font-weight: 500; padding-top: 8px;">明細項目</label>
              <div class="flex-grow-1 ms-2">
                <!-- 編輯模式：顯示已選擇的明細（禁止異動） -->
                <div *ngIf="isEditMode" class="mb-3">
                  <div *ngIf="isLoadingExistingDetails" class="text-center py-4">
                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
                    <span class="text-muted">載入已選擇明細...</span>
                  </div>
                  <div *ngIf="!isLoadingExistingDetails" class="existing-details-display">
                    <div class="alert alert-info mb-3" style="border-left: 4px solid #4a90e2;">
                      <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle text-primary me-2"></i>
                        <span>編輯模式下無法異動明細項目，僅可修改模板基本資訊</span>
                      </div>
                    </div>
                    <div *ngIf="selectedSpacesForTemplate.length === 0 && selectedItemsForTemplate.length === 0"
                      class="text-muted text-center py-3">
                      <i class="fas fa-inbox me-2"></i>此模板尚無明細項目
                    </div>
                    <!-- 統一的唯讀明細項目顯示（空間模板和項目模板） -->
                    <div *ngIf="getReadonlyItems().length > 0" class="selected-items-readonly">
                      <div class="readonly-items-grid">
                        <div *ngFor="let item of getReadonlyItems(); let i = index" class="readonly-item-card mb-3">
                          <div class="card border-0 shadow-sm h-100" style="border-radius: 12px; overflow: hidden;">
                            <!-- 卡片頭部 -->
                            <div class="card-header bg-light border-0 py-3">
                              <div class="d-flex align-items-center justify-content-between">
                                <div class="item-info flex-grow-1">
                                  <div class="item-name font-weight-bold text-dark mb-1">
                                    <i class="fas me-2"
                                      [class.fa-home]="templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate"
                                      [class.fa-cube]="templateDetail.CTemplateType === EnumTemplateType.ItemTemplate"
                                      [class.text-success]="templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate"
                                      [class.text-primary]="templateDetail.CTemplateType === EnumTemplateType.ItemTemplate"></i>{{
                                    item.CPart }}
                                  </div>
                                  <div class="item-location text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i>{{ item.CLocation || '-' }}
                                  </div>
                                </div>
                                <span class="badge badge-secondary px-3 py-2">
                                  <i class="fas fa-lock me-1"></i>已鎖定
                                </span>
                              </div>
                            </div>

                            <!-- 卡片內容（僅項目模板顯示單價單位） -->
                            <div *ngIf="templateDetail.CTemplateType === EnumTemplateType.ItemTemplate"
                              class="card-body py-3">
                              <div class="row g-3">
                                <!-- 單價資訊 -->
                                <div class="col-6">
                                  <div class="info-item">
                                    <label class="info-label text-muted small mb-1 d-block">
                                      <i class="fas fa-dollar-sign me-1 text-success"></i>單價
                                    </label>
                                    <div class="info-value bg-light rounded px-3 py-2 text-center">
                                      <span class="text-dark font-weight-bold">{{ getItemPrice(item) | number:'1.2-2'
                                        }}</span>
                                    </div>
                                  </div>
                                </div>

                                <!-- 單位資訊 -->
                                <div class="col-6">
                                  <div class="info-item">
                                    <label class="info-label text-muted small mb-1 d-block">
                                      <i class="fas fa-tag me-1 text-info"></i>單位
                                    </label>
                                    <div class="info-value bg-light rounded px-3 py-2 text-center">
                                      <span class="text-dark font-weight-bold">{{ getItemUnit(item) }}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- 卡片底部（項目編號） -->
                            <div class="card-footer bg-white border-0 py-2">
                              <div class="text-center">
                                <small class="text-muted">
                                  {{ templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate ? '空間' : '項目' }}
                                  #{{ i + 1 }}
                                </small>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 新增模式：正常的空間/項目選擇器 -->
                <div *ngIf="!isEditMode" class="mb-3">
                  <app-space-picker
                    [selectedItems]="templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate ? selectedSpacesForTemplate : selectedItemsForTemplate"
                    [multiple]="true" (selectionChange)="onSelectionChange($event)">
                  </app-space-picker>
                </div>
              </div>
            </div>
            <div class="col-md-12">

              <!-- 新增模式：項目模板的單價單位設定區域 -->
              <div
                *ngIf="!isEditMode && templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && selectedItemsForTemplate.length > 0"
                class="selected-items-config">
                <div class="config-header mb-3">
                  <h6 class="mb-0 text-primary font-weight-bold">
                    <i class="fas fa-cog me-2"></i>項目單價單位設定
                    <span class="badge badge-info ms-2">{{ getValidItemsCount() }}/{{
                      selectedItemsForTemplate.length }} 已完成</span>
                  </h6>
                  <small class="text-muted">請為每個選中的項目設定單價和單位</small>
                </div>
                <div class="config-items">
                  <div *ngFor="let item of selectedItemsForTemplate; let i = index"
                    class="config-item mb-3 p-3 border rounded" [class.valid]="isItemValid(item)"
                    [class.invalid]="!isItemValid(item)">
                    <div class="row align-items-center">
                      <!-- 項目資訊 -->
                      <div class="col-md-4">
                        <div class="item-info">
                          <div class="item-name font-weight-bold">
                            <i class="fas fa-cube me-2 text-primary"></i>{{ item.CPart }}
                          </div>
                          <div class="item-location text-muted small">
                            <i class="fas fa-map-marker-alt me-1"></i>{{ item.CLocation || '-' }}
                          </div>
                        </div>
                      </div>

                      <!-- 單價設定 -->
                      <div class="col-md-3">
                        <label class="form-label small text-muted mb-1">單價 <span class="text-danger">*</span></label>
                        <nb-form-field>
                          <input type="number" nbInput size="small" placeholder="請輸入單價" [(ngModel)]="item.CUnitPrice"
                            (ngModelChange)="onPriceChange(item, $event)" min="0" step="0.01"
                            [class.is-invalid]="!item.CUnitPrice || item.CUnitPrice <= 0" style="text-align: right;" />
                          <nb-icon nbSuffix icon="dollar-sign-outline" class="text-success"></nb-icon>
                        </nb-form-field>
                        <div *ngIf="!item.CUnitPrice || item.CUnitPrice <= 0" class="invalid-feedback small">
                          單價必須大於0
                        </div>
                      </div>

                      <!-- 單位設定 -->
                      <div class="col-md-3">
                        <label class="form-label small text-muted mb-1">單位 <span class="text-danger">*</span></label>
                        <nb-form-field>
                          <input type="text" nbInput size="small" placeholder="請輸入單位" [(ngModel)]="item.CUnit"
                            maxlength="10"
                            [class.is-invalid]="!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式'" />
                          <nb-icon nbSuffix icon="tag-outline" class="text-info"></nb-icon>
                        </nb-form-field>
                        <div *ngIf="!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式'"
                          class="invalid-feedback small">
                          請輸入具體單位（不能為空或"式"）
                        </div>
                      </div>

                      <!-- 移除按鈕 -->
                      <div class="col-md-2 text-end">
                        <button type="button" class="btn btn-outline-danger btn-sm" (click)="removeSelectedItem(item)"
                          title="移除此項目">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>

                    <!-- 驗證狀態指示 -->
                    <div class="validation-status mt-2">
                      <div *ngIf="isItemValid(item)" class="text-success small">
                        <i class="fas fa-check-circle me-1"></i>設定完成
                      </div>
                      <div *ngIf="!isItemValid(item)" class="text-warning small">
                        <i class="fas fa-exclamation-triangle me-1"></i>請完成單價和單位設定
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 設定進度提示 -->
                <div class="config-summary mt-3 p-3 bg-light border rounded">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="summary-item">
                        <span class="text-muted">已選擇項目：</span>
                        <span class="font-weight-bold text-primary">{{ selectedItemsForTemplate.length }} 個</span>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="summary-item">
                        <span class="text-muted">已完成設定：</span>
                        <span class="font-weight-bold"
                          [class.text-success]="getValidItemsCount() === selectedItemsForTemplate.length"
                          [class.text-warning]="getValidItemsCount() < selectedItemsForTemplate.length">
                          {{ getValidItemsCount() }} 個
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="progress mt-2" style="height: 6px;">
                    <div class="progress-bar bg-success"
                      [style.width.%]="selectedItemsForTemplate.length > 0 ? (getValidItemsCount() / selectedItemsForTemplate.length) * 100 : 0">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-end border-top pt-3 px-4 pb-3" style="background-color: #f8f9fa;">
      <button class="btn btn-outline-secondary me-3 px-4" (click)="onClose(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px;">
        <i class="fas fa-times me-1"></i>取消
      </button>
      <button class="btn btn-primary px-4" (click)="onSubmit(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);">
        <i class="fas fa-check me-1"></i>{{ isEditMode ? '更新模板' : '建立模板' }}
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>



<!-- 查看模板明細模態框 -->
<ng-template #templateDetailModal let-ref="dialogRef">
  <nb-card style="width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);">
    <nb-card-header class="d-flex justify-content-between align-items-center border-bottom py-3 px-4">
      <h5 class="mb-0 text-primary font-weight-bold">
        <i class="fas fa-eye me-2 text-info"></i>模板明細
      </h5>
      <button type="button" class="btn btn-ghost-light btn-sm rounded-circle" (click)="onClose(ref)"
        style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
        <i class="fas fa-times"></i>
      </button>
    </nb-card-header>

    <nb-card-body class="px-4 py-4">
      <!-- 模板基本資訊 -->
      <div class="card mb-4" style="border: 1px solid #e4e7ea; border-radius: 8px;">
        <div class="card-header" style="background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;">
          <h6 class="mb-0 text-dark font-weight-bold">
            <i class="fas fa-info-circle me-2 text-primary"></i>基本資訊
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-tag me-2 text-primary"></i>模板名稱
                </label>
                <p class="mb-0">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-toggle-on me-2 text-success"></i>狀態
                </label>
                <p class="mb-0">
                  {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}
                </p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-calendar-plus me-2 text-info"></i>建立時間
                </label>
                <p class="mb-0">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-user-plus me-2 text-warning"></i>建立者
                </label>
                <p class="mb-0">{{ selectedTemplateDetail?.CCreator || '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-calendar-edit me-2 text-info"></i>更新時間
                </label>
                <p class="mb-0">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-3">
                <label class="font-weight-bold text-muted">
                  <i class="fas fa-user-edit me-2 text-warning"></i>更新者
                </label>
                <p class="mb-0">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 包含的空間列表 -->
      <div class="card" style="border: 1px solid #e4e7ea; border-radius: 8px;">
        <div class="card-header d-flex justify-content-between align-items-center"
          style="background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;">
          <h6 class="mb-0 text-dark font-weight-bold">
            <i class="fas fa-home me-2 text-success"></i>包含的空間
          </h6>
          <span class="badge badge-info">共 {{ templateDetailSpaces.length }} 個空間</span>
        </div>
        <div class="card-body">
          <!-- Loading 狀態 -->
          <div *ngIf="isLoadingTemplateDetail" class="text-center py-4">
            <i class="fas fa-spinner fa-spin me-2 text-primary" style="font-size: 1.2rem;"></i>
            <span class="text-muted">載入中...</span>
          </div>

          <!-- 空間列表 -->
          <div *ngIf="!isLoadingTemplateDetail">
            <div class="table-responsive" *ngIf="templateDetailSpaces.length > 0">
              <table class="table table-sm">
                <thead>
                  <tr>
                    <th scope="col" class="col-1">#</th>
                    <th scope="col" class="col-7">項目名稱</th>
                    <th scope="col" class="col-4">所屬區域</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let space of templateDetailSpaces; let i = index">
                    <td>{{ i + 1 }}</td>
                    <td>
                      <i class="fas fa-home me-2 text-muted"></i>{{ space.CPart }}
                    </td>
                    <td>
                      <i class="fas fa-map-marker-alt me-2 text-muted"></i>{{ space.CLocation || '-' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- 沒有空間時的提示 -->
            <div *ngIf="templateDetailSpaces.length === 0" class="text-center text-muted py-4">
              <i class="fas fa-info-circle me-2"></i>此模板尚未包含任何空間
            </div>
          </div>
        </div>
      </div>
    </nb-card-body>

    <nb-card-footer class="d-flex justify-content-end border-top pt-3 px-4 pb-3" style="background-color: #f8f9fa;">
      <button class="btn btn-secondary px-4" (click)="onClose(ref)"
        style="min-width: 80px; height: 38px; border-radius: 6px;">
        <i class="fas fa-times me-1"></i>關閉
      </button>
    </nb-card-footer>
  </nb-card>
</ng-template>