{"ast": null, "code": "import { BaseComponent } from '../../components/base/baseComponent';\nimport { CommonModule } from '@angular/common';\nimport { SharedModule } from '../../components/shared.module';\nimport { SpacePickerComponent } from 'src/app/shared/components/space-picker/space-picker.component';\nimport { tap } from 'rxjs';\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/shared/helper/allowHelper\";\nimport * as i2 from \"@nebular/theme\";\nimport * as i3 from \"src/services/api/services\";\nimport * as i4 from \"src/app/shared/services/message.service\";\nimport * as i5 from \"src/app/shared/helper/validationHelper\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"../../components/breadcrumb/breadcrumb.component\";\nimport * as i9 from \"../../components/pagination/pagination.component\";\nconst _c0 = [\"createModal\"];\nconst _c1 = [\"editModal\"];\nconst _c2 = [\"templateDetailModal\"];\nfunction TemplateComponent_button_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_button_52_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateModal_r4 = i0.ɵɵreference(75);\n      return i0.ɵɵresetView(ctx_r2.openCreateModal(templateModal_r4));\n    });\n    i0.ɵɵelement(1, \"i\", 38);\n    i0.ɵɵtext(2, \"\\u65B0\\u589E\\u6A21\\u677F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateModal_r4 = i0.ɵɵreference(75);\n      return i0.ɵɵresetView(ctx_r2.openEditModal(templateModal_r4, template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"\\u7DE8\\u8F2F \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_button_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_button_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const template_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.deleteTemplate(template_r6));\n    });\n    i0.ɵɵelement(1, \"i\", 48);\n    i0.ɵɵtext(2, \"\\u522A\\u9664 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_tr_70_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 39);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 40)(14, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_tr_70_Template_button_click_14_listener() {\n      const template_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      const templateDetailModal_r7 = i0.ɵɵreference(77);\n      return i0.ɵɵresetView(ctx_r2.viewTemplateDetail(template_r6, templateDetailModal_r7));\n    });\n    i0.ɵɵelement(15, \"i\", 42);\n    i0.ɵɵtext(16, \"\\u67E5\\u770B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, TemplateComponent_tr_70_button_17_Template, 3, 0, \"button\", 43)(18, TemplateComponent_tr_70_button_18_Template, 3, 0, \"button\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const template_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CTemplateType === 1 ? \"\\u7A7A\\u9593\\u6A21\\u677F\" : template_r6.CTemplateType === 2 ? \"\\u9805\\u76EE\\u6A21\\u677F\" : \"-\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(template_r6.CTemplateName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", template_r6.CStatus === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", template_r6.CStatus === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, template_r6.CCreateDt, \"yyyy/MM/dd HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(template_r6.CCreator || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isUpdate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isDelete);\n  }\n}\nfunction TemplateComponent_tr_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 49);\n    i0.ɵɵelement(2, \"i\", 50);\n    i0.ɵɵtext(3, \"\\u76EE\\u524D\\u6C92\\u6709\\u4EFB\\u4F55\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵelement(1, \"i\", 85);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate ? \"\\u7A7A\\u9593\\u6A21\\u677F\" : \"\\u9805\\u76EE\\u6A21\\u677F\");\n  }\n}\nfunction TemplateComponent_ng_template_74_nb_form_field_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-form-field\", 67)(1, \"nb-select\", 86);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_nb_form_field_40_Template_nb_select_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateType, $event) || (ctx_r2.templateDetail.CTemplateType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(2, \"nb-option\", 15)(3, \"span\", 4);\n    i0.ɵɵelement(4, \"i\", 87);\n    i0.ɵɵtext(5, \"\\u7A7A\\u9593\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"nb-option\", 15)(7, \"span\", 4);\n    i0.ɵɵelement(8, \"i\", 88);\n    i0.ɵɵtext(9, \"\\u9805\\u76EE\\u6A21\\u677F \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r2.EnumTemplateType.SpaceTemplate);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", ctx_r2.EnumTemplateType.ItemTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_49_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵelement(1, \"div\", 93);\n    i0.ɵɵelementStart(2, \"span\", 94);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u5DF2\\u9078\\u64C7\\u660E\\u7D30...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_49_div_2_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵelement(1, \"i\", 100);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u7121\\u660E\\u7D30\\u9805\\u76EE \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_49_div_2_div_7_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"div\", 4)(2, \"div\", 109)(3, \"div\", 110);\n    i0.ɵɵelement(4, \"i\", 111);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 112);\n    i0.ɵɵelement(7, \"i\", 113);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"span\", 114);\n    i0.ɵɵelement(10, \"i\", 115);\n    i0.ɵɵtext(11, \"\\u5DF2\\u9396\\u5B9A \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const space_r13 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", space_r13.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r13.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_49_div_2_div_7_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_74_div_49_div_2_div_7_div_6_div_1_Template, 12, 2, \"div\", 107);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedSpacesForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_49_div_2_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"h6\", 102);\n    i0.ɵɵelement(2, \"i\", 103);\n    i0.ɵɵtext(3, \"\\u5DF2\\u9078\\u64C7\\u660E\\u7D30 \");\n    i0.ɵɵelementStart(4, \"span\", 104);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, TemplateComponent_ng_template_74_div_49_div_2_div_7_div_6_Template, 2, 1, \"div\", 105);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedSpacesForTemplate.length || ctx_r2.selectedItemsForTemplate.length, \" \\u9805 \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate && ctx_r2.selectedSpacesForTemplate.length > 0);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_49_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"i\", 97);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"\\u7DE8\\u8F2F\\u6A21\\u5F0F\\u4E0B\\u7121\\u6CD5\\u7570\\u52D5\\u660E\\u7D30\\u9805\\u76EE\\uFF0C\\u50C5\\u53EF\\u4FEE\\u6539\\u6A21\\u677F\\u57FA\\u672C\\u8CC7\\u8A0A\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(6, TemplateComponent_ng_template_74_div_49_div_2_div_6_Template, 3, 0, \"div\", 98)(7, TemplateComponent_ng_template_74_div_49_div_2_div_7_Template, 7, 2, \"div\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length === 0 && ctx_r2.selectedItemsForTemplate.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedSpacesForTemplate.length > 0 || ctx_r2.selectedItemsForTemplate.length > 0);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_74_div_49_div_1_Template, 4, 0, \"div\", 90)(2, TemplateComponent_ng_template_74_div_49_div_2_Template, 8, 2, \"div\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingExistingDetails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingExistingDetails);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"app-space-picker\", 116);\n    i0.ɵɵlistener(\"selectionChange\", function TemplateComponent_ng_template_74_div_50_Template_app_space_picker_selectionChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSelectionChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"selectedItems\", ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.SpaceTemplate ? ctx_r2.selectedSpacesForTemplate : ctx_r2.selectedItemsForTemplate)(\"multiple\", true);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_52_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"div\", 123)(2, \"div\", 124)(3, \"div\", 125)(4, \"div\", 110);\n    i0.ɵɵelement(5, \"i\", 126);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 112);\n    i0.ɵɵelement(8, \"i\", 113);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 127)(11, \"label\", 128);\n    i0.ɵɵtext(12, \"\\u55AE\\u50F9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 129)(14, \"span\", 130);\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"i\", 131);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 127)(19, \"label\", 128);\n    i0.ɵɵtext(20, \"\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 129)(22, \"span\", 130);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"i\", 132);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 133)(26, \"span\", 114);\n    i0.ɵɵelement(27, \"i\", 115);\n    i0.ɵɵtext(28, \"\\u5DF2\\u9396\\u5B9A \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r15 = ctx.$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", item_r15.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", item_r15.CLocation || \"-\", \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 4, item_r15.CUnitPrice || 0, \"1.2-2\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(item_r15.CUnit || \"-\");\n  }\n}\nfunction TemplateComponent_ng_template_74_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"div\", 117)(2, \"h6\", 118);\n    i0.ɵɵelement(3, \"i\", 119);\n    i0.ɵɵtext(4, \"\\u5DF2\\u9078\\u64C7\\u9805\\u76EE\\u660E\\u7D30\\uFF08\\u53EA\\u8B80\\uFF09 \");\n    i0.ɵɵelementStart(5, \"span\", 104);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"small\", 94);\n    i0.ɵɵtext(8, \"\\u7DE8\\u8F2F\\u6A21\\u5F0F\\u4E0B\\u7121\\u6CD5\\u4FEE\\u6539\\u9805\\u76EE\\u660E\\u7D30\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 120);\n    i0.ɵɵtemplate(10, TemplateComponent_ng_template_74_div_52_div_10_Template, 29, 7, \"div\", 121);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.selectedItemsForTemplate.length, \" \\u9805\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedItemsForTemplate);\n  }\n}\nfunction TemplateComponent_ng_template_74_div_53_div_10_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157);\n    i0.ɵɵtext(1, \" \\u55AE\\u50F9\\u5FC5\\u9808\\u5927\\u65BC0 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_53_div_10_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 157);\n    i0.ɵɵtext(1, \" \\u8ACB\\u8F38\\u5165\\u5177\\u9AD4\\u55AE\\u4F4D\\uFF08\\u4E0D\\u80FD\\u70BA\\u7A7A\\u6216\\\"\\u5F0F\\\"\\uFF09 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_53_div_10_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 158);\n    i0.ɵɵelement(1, \"i\", 159);\n    i0.ɵɵtext(2, \"\\u8A2D\\u5B9A\\u5B8C\\u6210 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_53_div_10_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 160);\n    i0.ɵɵelement(1, \"i\", 161);\n    i0.ɵɵtext(2, \"\\u8ACB\\u5B8C\\u6210\\u55AE\\u50F9\\u548C\\u55AE\\u4F4D\\u8A2D\\u5B9A \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_74_div_53_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 144)(1, \"div\", 123)(2, \"div\", 124)(3, \"div\", 125)(4, \"div\", 110);\n    i0.ɵɵelement(5, \"i\", 145);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 112);\n    i0.ɵɵelement(8, \"i\", 113);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 127)(11, \"label\", 128);\n    i0.ɵɵtext(12, \"\\u55AE\\u50F9 \");\n    i0.ɵɵelementStart(13, \"span\", 146);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"nb-form-field\")(16, \"input\", 147);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_div_53_div_10_Template_input_ngModelChange_16_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r17.CUnitPrice, $event) || (item_r17.CUnitPrice = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function TemplateComponent_ng_template_74_div_53_div_10_Template_input_ngModelChange_16_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onPriceChange(item_r17, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"nb-icon\", 148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, TemplateComponent_ng_template_74_div_53_div_10_div_18_Template, 2, 0, \"div\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 127)(20, \"label\", 128);\n    i0.ɵɵtext(21, \"\\u55AE\\u4F4D \");\n    i0.ɵɵelementStart(22, \"span\", 146);\n    i0.ɵɵtext(23, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"nb-form-field\")(25, \"input\", 150);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_div_53_div_10_Template_input_ngModelChange_25_listener($event) {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r17.CUnit, $event) || (item_r17.CUnit = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"nb-icon\", 151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, TemplateComponent_ng_template_74_div_53_div_10_div_27_Template, 2, 0, \"div\", 149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 152)(29, \"button\", 153);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_div_53_div_10_Template_button_click_29_listener() {\n      const item_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.removeSelectedItem(item_r17));\n    });\n    i0.ɵɵelement(30, \"i\", 48);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 154);\n    i0.ɵɵtemplate(32, TemplateComponent_ng_template_74_div_53_div_10_div_32_Template, 3, 0, \"div\", 155)(33, TemplateComponent_ng_template_74_div_53_div_10_div_33_Template, 3, 0, \"div\", 156);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"valid\", ctx_r2.isItemValid(item_r17))(\"invalid\", !ctx_r2.isItemValid(item_r17));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", item_r17.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", item_r17.CLocation || \"-\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", !item_r17.CUnitPrice || item_r17.CUnitPrice <= 0);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r17.CUnitPrice);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !item_r17.CUnitPrice || item_r17.CUnitPrice <= 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"is-invalid\", !item_r17.CUnit || item_r17.CUnit.trim() === \"\" || item_r17.CUnit.trim() === \"\\u5F0F\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r17.CUnit);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !item_r17.CUnit || item_r17.CUnit.trim() === \"\" || item_r17.CUnit.trim() === \"\\u5F0F\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemValid(item_r17));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isItemValid(item_r17));\n  }\n}\nfunction TemplateComponent_ng_template_74_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 134)(1, \"div\", 117)(2, \"h6\", 53);\n    i0.ɵɵelement(3, \"i\", 135);\n    i0.ɵɵtext(4, \"\\u9805\\u76EE\\u55AE\\u50F9\\u55AE\\u4F4D\\u8A2D\\u5B9A \");\n    i0.ɵɵelementStart(5, \"span\", 136);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"small\", 94);\n    i0.ɵɵtext(8, \"\\u8ACB\\u70BA\\u6BCF\\u500B\\u9078\\u4E2D\\u7684\\u9805\\u76EE\\u8A2D\\u5B9A\\u55AE\\u50F9\\u548C\\u55AE\\u4F4D\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 120);\n    i0.ɵɵtemplate(10, TemplateComponent_ng_template_74_div_53_div_10_Template, 34, 16, \"div\", 137);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 138)(12, \"div\", 58)(13, \"div\", 8)(14, \"div\", 139)(15, \"span\", 94);\n    i0.ɵɵtext(16, \"\\u5DF2\\u9078\\u64C7\\u9805\\u76EE\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 140);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 8)(20, \"div\", 139)(21, \"span\", 94);\n    i0.ɵɵtext(22, \"\\u5DF2\\u5B8C\\u6210\\u8A2D\\u5B9A\\uFF1A\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 141);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 142);\n    i0.ɵɵelement(26, \"div\", 143);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getValidItemsCount(), \"/\", ctx_r2.selectedItemsForTemplate.length, \" \\u5DF2\\u5B8C\\u6210\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedItemsForTemplate);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.selectedItemsForTemplate.length, \" \\u500B\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵclassProp(\"text-success\", ctx_r2.getValidItemsCount() === ctx_r2.selectedItemsForTemplate.length)(\"text-warning\", ctx_r2.getValidItemsCount() < ctx_r2.selectedItemsForTemplate.length);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getValidItemsCount(), \" \\u500B \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r2.selectedItemsForTemplate.length > 0 ? ctx_r2.getValidItemsCount() / ctx_r2.selectedItemsForTemplate.length * 100 : 0, \"%\");\n  }\n}\nfunction TemplateComponent_ng_template_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 51)(1, \"nb-card-header\", 52)(2, \"h5\", 53);\n    i0.ɵɵelement(3, \"i\", 54);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_5_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r11));\n    });\n    i0.ɵɵelement(6, \"i\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 57)(8, \"div\", 58)(9, \"div\", 59)(10, \"div\", 60)(11, \"div\", 61)(12, \"div\", 8)(13, \"div\", 62)(14, \"label\", 63);\n    i0.ɵɵtext(15, \"\\u6A21\\u677F\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 64)(17, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_input_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CTemplateName, $event) || (ctx_r2.templateDetail.CTemplateName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown.control.enter\", function TemplateComponent_ng_template_74_Template_input_keydown_control_enter_17_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r11));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(18, \"div\", 8)(19, \"div\", 62)(20, \"label\", 66);\n    i0.ɵɵtext(21, \"\\u72C0\\u614B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 64)(23, \"nb-form-field\", 67)(24, \"nb-select\", 68);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.templateDetail.CStatus, $event) || (ctx_r2.templateDetail.CStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(25, \"nb-option\", 15)(26, \"span\", 4);\n    i0.ɵɵelement(27, \"i\", 69);\n    i0.ɵɵtext(28, \"\\u555F\\u7528 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"nb-option\", 15)(30, \"span\", 4);\n    i0.ɵɵelement(31, \"i\", 70);\n    i0.ɵɵtext(32, \"\\u505C\\u7528 \");\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(33, \"div\", 61)(34, \"div\", 8)(35, \"div\", 4)(36, \"label\", 71);\n    i0.ɵɵtext(37, \"\\u6A21\\u677F\\u985E\\u578B\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 64);\n    i0.ɵɵtemplate(39, TemplateComponent_ng_template_74_div_39_Template, 4, 1, \"div\", 72)(40, TemplateComponent_ng_template_74_nb_form_field_40_Template, 10, 3, \"nb-form-field\", 73);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(41, \"div\", 8);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 59)(43, \"div\", 60)(44, \"div\", 58)(45, \"div\", 74)(46, \"label\", 75);\n    i0.ɵɵtext(47, \"\\u660E\\u7D30\\u9805\\u76EE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 64);\n    i0.ɵɵtemplate(49, TemplateComponent_ng_template_74_div_49_Template, 3, 2, \"div\", 76)(50, TemplateComponent_ng_template_74_div_50_Template, 2, 2, \"div\", 76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 18);\n    i0.ɵɵtemplate(52, TemplateComponent_ng_template_74_div_52_Template, 11, 2, \"div\", 77)(53, TemplateComponent_ng_template_74_div_53_Template, 27, 11, \"div\", 78);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(54, \"nb-card-footer\", 79)(55, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_55_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r11));\n    });\n    i0.ɵɵelement(56, \"i\", 81);\n    i0.ɵɵtext(57, \"\\u53D6\\u6D88 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_74_Template_button_click_58_listener() {\n      const ref_r11 = i0.ɵɵrestoreView(_r10).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit(ref_r11));\n    });\n    i0.ɵɵelement(59, \"i\", 83);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"width\", ctx_r2.modalWidth);\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-plus-circle\", !ctx_r2.isEditMode)(\"fa-edit\", ctx_r2.isEditMode)(\"text-success\", !ctx_r2.isEditMode)(\"text-warning\", ctx_r2.isEditMode);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isEditMode ? \"\\u7DE8\\u8F2F\\u6A21\\u677F\" : \"\\u65B0\\u589E\\u6A21\\u677F\", \" \");\n    i0.ɵɵadvance(13);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CTemplateName);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.templateDetail.CStatus);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEditMode);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEditMode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isEditMode && ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate && ctx_r2.selectedItemsForTemplate.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isEditMode && ctx_r2.templateDetail.CTemplateType === ctx_r2.EnumTemplateType.ItemTemplate && ctx_r2.selectedItemsForTemplate.length > 0);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.isEditMode ? \"\\u66F4\\u65B0\\u6A21\\u677F\" : \"\\u5EFA\\u7ACB\\u6A21\\u677F\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_76_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92);\n    i0.ɵɵelement(1, \"i\", 184);\n    i0.ɵɵelementStart(2, \"span\", 94);\n    i0.ɵɵtext(3, \"\\u8F09\\u5165\\u4E2D...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction TemplateComponent_ng_template_76_div_70_div_1_tr_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵelement(4, \"i\", 192);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵelement(7, \"i\", 193);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const space_r20 = ctx.$implicit;\n    const i_r21 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r21 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r20.CPart, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", space_r20.CLocation || \"-\", \" \");\n  }\n}\nfunction TemplateComponent_ng_template_76_div_70_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 187)(1, \"table\", 188)(2, \"thead\")(3, \"tr\")(4, \"th\", 189);\n    i0.ɵɵtext(5, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 190);\n    i0.ɵɵtext(7, \"\\u9805\\u76EE\\u540D\\u7A31\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 191);\n    i0.ɵɵtext(9, \"\\u6240\\u5C6C\\u5340\\u57DF\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"tbody\");\n    i0.ɵɵtemplate(11, TemplateComponent_ng_template_76_div_70_div_1_tr_11_Template, 9, 3, \"tr\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.templateDetailSpaces);\n  }\n}\nfunction TemplateComponent_ng_template_76_div_70_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 194);\n    i0.ɵɵelement(1, \"i\", 50);\n    i0.ɵɵtext(2, \"\\u6B64\\u6A21\\u677F\\u5C1A\\u672A\\u5305\\u542B\\u4EFB\\u4F55\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TemplateComponent_ng_template_76_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, TemplateComponent_ng_template_76_div_70_div_1_Template, 12, 1, \"div\", 185)(2, TemplateComponent_ng_template_76_div_70_div_2_Template, 3, 0, \"div\", 186);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.templateDetailSpaces.length === 0);\n  }\n}\nfunction TemplateComponent_ng_template_76_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nb-card\", 162)(1, \"nb-card-header\", 52)(2, \"h5\", 53);\n    i0.ɵɵelement(3, \"i\", 163);\n    i0.ɵɵtext(4, \"\\u6A21\\u677F\\u660E\\u7D30 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_5_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r19));\n    });\n    i0.ɵɵelement(6, \"i\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"nb-card-body\", 57)(8, \"div\", 164)(9, \"div\", 165)(10, \"h6\", 166);\n    i0.ɵɵelement(11, \"i\", 167);\n    i0.ɵɵtext(12, \"\\u57FA\\u672C\\u8CC7\\u8A0A \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 168)(14, \"div\", 58)(15, \"div\", 8)(16, \"div\", 169)(17, \"label\", 170);\n    i0.ɵɵelement(18, \"i\", 171);\n    i0.ɵɵtext(19, \"\\u6A21\\u677F\\u540D\\u7A31 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 172);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 8)(23, \"div\", 169)(24, \"label\", 170);\n    i0.ɵɵelement(25, \"i\", 173);\n    i0.ɵɵtext(26, \"\\u72C0\\u614B \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"p\", 172)(28, \"span\", 39);\n    i0.ɵɵelement(29, \"i\", 174);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(31, \"div\", 8)(32, \"div\", 169)(33, \"label\", 170);\n    i0.ɵɵelement(34, \"i\", 175);\n    i0.ɵɵtext(35, \"\\u5EFA\\u7ACB\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"p\", 172);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 8)(40, \"div\", 169)(41, \"label\", 170);\n    i0.ɵɵelement(42, \"i\", 176);\n    i0.ɵɵtext(43, \"\\u5EFA\\u7ACB\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"p\", 172);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(46, \"div\", 8)(47, \"div\", 169)(48, \"label\", 170);\n    i0.ɵɵelement(49, \"i\", 177);\n    i0.ɵɵtext(50, \"\\u66F4\\u65B0\\u6642\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"p\", 172);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"div\", 8)(55, \"div\", 169)(56, \"label\", 170);\n    i0.ɵɵelement(57, \"i\", 178);\n    i0.ɵɵtext(58, \"\\u66F4\\u65B0\\u8005 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"p\", 172);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(61, \"div\", 179)(62, \"div\", 180)(63, \"h6\", 166);\n    i0.ɵɵelement(64, \"i\", 181);\n    i0.ɵɵtext(65, \"\\u5305\\u542B\\u7684\\u7A7A\\u9593 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 182);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 168);\n    i0.ɵɵtemplate(69, TemplateComponent_ng_template_76_div_69_Template, 4, 0, \"div\", 90)(70, TemplateComponent_ng_template_76_div_70_Template, 3, 2, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(71, \"nb-card-footer\", 79)(72, \"button\", 183);\n    i0.ɵɵlistener(\"click\", function TemplateComponent_ng_template_76_Template_button_click_72_listener() {\n      const ref_r19 = i0.ɵɵrestoreView(_r18).dialogRef;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClose(ref_r19));\n    });\n    i0.ɵɵelement(73, \"i\", 81);\n    i0.ɵɵtext(74, \"\\u95DC\\u9589 \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CTemplateName) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"badge-success\" : \"badge-secondary\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"fas fa-check-circle\" : \"fas fa-times-circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CStatus) === 1 ? \"\\u555F\\u7528\" : \"\\u505C\\u7528\", \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(38, 12, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CCreator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(53, 15, ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdateDt, \"yyyy/MM/dd HH:mm\") || \"-\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((ctx_r2.selectedTemplateDetail == null ? null : ctx_r2.selectedTemplateDetail.CUpdator) || \"-\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\\u5171 \", ctx_r2.templateDetailSpaces.length, \" \\u500B\\u7A7A\\u9593\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingTemplateDetail);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoadingTemplateDetail);\n  }\n}\nexport class TemplateComponent extends BaseComponent {\n  constructor(allow, dialogService, _templateService, _spaceService, message, valid) {\n    super(allow);\n    this.allow = allow;\n    this.dialogService = dialogService;\n    this._templateService = _templateService;\n    this._spaceService = _spaceService;\n    this.message = message;\n    this.valid = valid;\n    this.Math = Math; // 讓模板可以使用 Math 函數\n    this.EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\n    this.EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\n    this.pageFirst = 1;\n    this.pageSize = 10;\n    this.pageIndex = 1;\n    this.totalRecords = 0;\n    // 模板相關屬性\n    this.templateList = [];\n    this.templateDetail = {};\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.searchTemplateType = null;\n    // 空間選擇相關屬性\n    this.availableSpaces = [];\n    this.selectedSpacesForTemplate = [];\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.spacePageSize = 10;\n    this.spaceTotalRecords = 0;\n    this.allSpacesSelected = false;\n    // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\n    this.availableItemsForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.itemPageSize = 10;\n    this.itemTotalRecords = 0;\n    this.allItemsSelected = false;\n    // 模板明細相關屬性\n    this.selectedTemplateDetail = null;\n    this.templateDetailSpaces = [];\n    this.isLoadingTemplateDetail = false;\n    // 編輯模式相關屬性\n    this.existingTemplateDetails = [];\n    this.isLoadingExistingDetails = false;\n    // 模態框模式控制\n    this.isEditMode = false;\n  }\n  ngOnInit() {\n    this.loadTemplateList();\n    this.loadAvailableSpaces();\n  }\n  // 載入模板列表\n  loadTemplateList() {\n    const request = {\n      CTemplateName: this.searchKeyword || null,\n      CStatus: this.searchStatus,\n      CTemplateType: this.searchTemplateType,\n      PageIndex: this.pageIndex,\n      PageSize: this.pageSize\n    };\n    this._templateService.apiTemplateGetTemplateListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.templateList = response.Entries?.map(item => ({\n          CTemplateId: item.CTemplateId,\n          CTemplateName: item.CTemplateName,\n          CTemplateType: item.CTemplateType,\n          // 新增模板類型\n          CCreateDt: item.CCreateDt,\n          CUpdateDt: item.CUpdateDt,\n          CCreator: item.CCreator,\n          CUpdator: item.CUpdator,\n          CStatus: item.CStatus\n        })) || [];\n        this.totalRecords = response.TotalItems || 0;\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板列表失敗');\n      }\n    })).subscribe();\n  }\n  // 載入可用空間列表\n  loadAvailableSpaces() {\n    const request = {\n      CPart: this.spaceSearchKeyword || null,\n      CLocation: this.spaceSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.spacePageIndex,\n      PageSize: this.spacePageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableSpaces = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\n        })) || [];\n        this.spaceTotalRecords = response.TotalItems || 0;\n        this.updateAllSpacesSelectedState();\n      }\n    })).subscribe();\n  }\n  // 搜尋功能\n  onSearch() {\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  onReset() {\n    this.searchKeyword = '';\n    this.searchStatus = null;\n    this.pageIndex = 1;\n    this.loadTemplateList();\n  }\n  // 載入項目模板可用項目（使用空間列表作為基礎）\n  loadAvailableItemsForTemplate() {\n    const request = {\n      CPart: this.itemSearchKeyword || null,\n      CLocation: this.itemSearchLocation || null,\n      CStatus: 1,\n      // 只顯示啟用的空間\n      PageIndex: this.itemPageIndex,\n      PageSize: this.itemPageSize\n    };\n    this._spaceService.apiSpaceGetSpaceListPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.availableItemsForTemplate = response.Entries?.map(item => ({\n          CSpaceID: item.CSpaceID,\n          CPart: item.CPart,\n          CLocation: item.CLocation,\n          selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\n          CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\n          CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\n        })) || [];\n        this.itemTotalRecords = response.TotalItems || 0;\n        this.updateAllItemsSelectedState();\n      }\n    })).subscribe();\n  }\n  // 空間搜尋功能\n  onSpaceSearch() {\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  onSpaceReset() {\n    this.spaceSearchKeyword = '';\n    this.spaceSearchLocation = '';\n    this.spacePageIndex = 1;\n    this.loadAvailableSpaces();\n  }\n  // 項目搜尋功能\n  onItemSearch() {\n    this.itemPageIndex = 1;\n    this.loadAvailableItemsForTemplate();\n  }\n  onItemReset() {\n    this.itemSearchKeyword = '';\n    this.itemSearchLocation = '';\n    this.itemPageIndex = 1;\n    this.loadAvailableItemsForTemplate();\n  }\n  // 分頁功能\n  pageChanged(page) {\n    this.pageIndex = page;\n    this.loadTemplateList();\n  }\n  spacePageChanged(page) {\n    this.spacePageIndex = page;\n    this.loadAvailableSpaces();\n  }\n  itemPageChanged(page) {\n    this.itemPageIndex = page;\n    this.loadAvailableItemsForTemplate();\n  }\n  // 模態框操作\n  openCreateModal(modal) {\n    this.isEditMode = false;\n    this.templateDetail = {\n      CStatus: 1,\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\n    };\n    this.selectedSpacesForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.loadAvailableSpaces();\n    this.loadAvailableItemsForTemplate();\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  openEditModal(modal, template) {\n    this.isEditMode = true;\n    this.templateDetail = {\n      CTemplateId: template.CTemplateId,\n      CTemplateName: template.CTemplateName,\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\n      CStatus: template.CStatus || 1\n    };\n    // 清空之前的選擇\n    this.selectedSpacesForTemplate = [];\n    this.selectedItemsForTemplate = [];\n    this.existingTemplateDetails = [];\n    // 載入已選擇的明細\n    this.loadExistingTemplateDetails(template.CTemplateId);\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n  }\n  onClose(ref) {\n    ref.close();\n  }\n  // 計算模態框寬度\n  get modalWidth() {\n    return '800px';\n  }\n  onSubmit(ref) {\n    if (!this.validateTemplateForm()) {\n      return;\n    }\n    if (this.templateDetail.CTemplateId) {\n      this.updateTemplate(ref);\n    } else {\n      this.createTemplate(ref);\n    }\n  }\n  // 驗證表單\n  validateTemplateForm() {\n    if (!this.templateDetail.CTemplateName?.trim()) {\n      this.message.showErrorMSG('請輸入模板名稱');\n      return false;\n    }\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\n      this.message.showErrorMSG('請選擇模板類型');\n      return false;\n    }\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\n      this.message.showErrorMSG('請選擇模板狀態');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\n      return false;\n    }\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\n      this.message.showErrorMSG('項目模板請至少選擇一個項目');\n      return false;\n    }\n    // 驗證項目模板的單價和單位\n    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      for (const item of this.selectedItemsForTemplate) {\n        // 檢核單價\n        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\n          return false;\n        }\n        // 檢核單價是否為有效數字\n        if (isNaN(item.CUnitPrice)) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\n          return false;\n        }\n        // 檢核單位\n        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\n          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\n          return false;\n        }\n        // 檢核單位長度\n        if (item.CUnit.trim().length > 10) {\n          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\n          return false;\n        }\n      }\n    }\n    return true;\n  }\n  // 建立模板\n  createTemplate(ref) {\n    let details = [];\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\n      // 空間模板的詳細資料\n      details = this.selectedSpacesForTemplate.map(space => ({\n        CTemplateDetailId: null,\n        CReleateId: space.CSpaceID,\n        CPart: space.CPart,\n        CLocation: space.CLocation\n      }));\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      // 項目模板的詳細資料，包含單價和單位\n      details = this.selectedItemsForTemplate.map(item => ({\n        CTemplateDetailId: null,\n        CReleateId: item.CSpaceID,\n        CPart: item.CPart,\n        CLocation: item.CLocation,\n        CUnitPrice: item.CUnitPrice,\n        CUnit: item.CUnit\n      }));\n    }\n    const templateData = {\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus,\n      Details: details.length > 0 ? details : undefined\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('建立模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '建立模板失敗');\n      }\n    })).subscribe();\n  }\n  // 更新模板\n  updateTemplate(ref) {\n    const templateData = {\n      CTemplateId: this.templateDetail.CTemplateId,\n      CTemplateName: this.templateDetail.CTemplateName,\n      CTemplateType: this.templateDetail.CTemplateType,\n      CStatus: this.templateDetail.CStatus\n    };\n    this._templateService.apiTemplateSaveTemplatePost$Json({\n      body: templateData\n    }).pipe(tap(response => {\n      if (response.StatusCode === 0) {\n        this.message.showSucessMSG('更新模板成功');\n        ref.close();\n        this.loadTemplateList();\n      } else {\n        this.message.showErrorMSG(response.Message || '更新模板失敗');\n      }\n    })).subscribe();\n  }\n  // 刪除模板\n  deleteTemplate(template) {\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\n        body: {\n          CTemplateId: template.CTemplateId\n        }\n      }).pipe(tap(response => {\n        if (response.StatusCode === 0) {\n          this.message.showSucessMSG('刪除模板成功');\n          this.loadTemplateList();\n        } else {\n          this.message.showErrorMSG(response.Message || '刪除模板失敗');\n        }\n      })).subscribe();\n    }\n  }\n  // 載入編輯模式下的已選擇明細\n  loadExistingTemplateDetails(templateId) {\n    this.isLoadingExistingDetails = true;\n    const request = {\n      templateId: templateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingExistingDetails = false;\n      if (response.StatusCode === 0) {\n        this.existingTemplateDetails = response.Entries || [];\n        // 根據模板類型設置已選擇的項目\n        if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\n          // 空間模板：設置已選擇的空間\n          this.selectedSpacesForTemplate = this.existingTemplateDetails.map(item => ({\n            CSpaceID: item.CReleateId,\n            CPart: item.CPart,\n            CLocation: item.CLocation,\n            selected: true\n          }));\n        } else {\n          // 項目模板：設置已選擇的項目（包含單價和單位）\n          this.selectedItemsForTemplate = this.existingTemplateDetails.map(item => ({\n            CSpaceID: item.CReleateId,\n            CPart: item.CPart,\n            CLocation: item.CLocation,\n            CUnitPrice: item.CUnitPrice || undefined,\n            CUnit: item.CUnit || undefined,\n            selected: true\n          }));\n        }\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 查看模板明細\n  viewTemplateDetail(template, modal) {\n    this.selectedTemplateDetail = template;\n    this.isLoadingTemplateDetail = true;\n    this.templateDetailSpaces = [];\n    this.dialogService.open(modal, {\n      context: {},\n      autoFocus: false\n    });\n    const request = {\n      templateId: template.CTemplateId\n    };\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({\n      body: request\n    }).pipe(tap(response => {\n      this.isLoadingTemplateDetail = false;\n      if (response.StatusCode === 0) {\n        this.templateDetailSpaces = response.Entries?.map(item => ({\n          CReleateId: item.CReleateId,\n          CPart: item.CPart,\n          CLocation: item.CLocation\n        })) || [];\n      } else {\n        this.message.showErrorMSG(response.Message || '載入模板明細失敗');\n      }\n    })).subscribe();\n  }\n  // 空間選擇相關方法\n  toggleSpaceSelection(space) {\n    space.selected = !space.selected;\n    if (space.selected) {\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n        this.selectedSpacesForTemplate.push({\n          ...space\n        });\n      }\n    } else {\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  toggleAllSpaces() {\n    this.allSpacesSelected = !this.allSpacesSelected;\n    this.availableSpaces.forEach(space => {\n      space.selected = this.allSpacesSelected;\n      if (this.allSpacesSelected) {\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\n          this.selectedSpacesForTemplate.push({\n            ...space\n          });\n        }\n      } else {\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n      }\n    });\n  }\n  removeSelectedSpace(space) {\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\n    if (availableSpace) {\n      availableSpace.selected = false;\n    }\n    this.updateAllSpacesSelectedState();\n  }\n  updateAllSpacesSelectedState() {\n    this.allSpacesSelected = this.availableSpaces.length > 0 && this.availableSpaces.every(space => space.selected);\n  }\n  // 項目選擇相關方法\n  toggleItemSelection(item) {\n    item.selected = !item.selected;\n    if (item.selected) {\n      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n        // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\n        const newItem = {\n          ...item,\n          CUnitPrice: 0,\n          // 設為0，強制用戶輸入\n          CUnit: '' // 設為空，強制用戶輸入\n        };\n        this.selectedItemsForTemplate.push(newItem);\n      }\n    } else {\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n    }\n    this.updateAllItemsSelectedState();\n  }\n  toggleAllItems() {\n    this.allItemsSelected = !this.allItemsSelected;\n    this.availableItemsForTemplate.forEach(item => {\n      item.selected = this.allItemsSelected;\n      if (this.allItemsSelected) {\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\n          // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\n          const newItem = {\n            ...item,\n            CUnitPrice: 0,\n            // 設為0，強制用戶輸入\n            CUnit: '' // 設為空，強制用戶輸入\n          };\n          this.selectedItemsForTemplate.push(newItem);\n        }\n      } else {\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n      }\n    });\n  }\n  removeSelectedItem(item) {\n    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.selected = false;\n    }\n    this.updateAllItemsSelectedState();\n  }\n  updateAllItemsSelectedState() {\n    this.allItemsSelected = this.availableItemsForTemplate.length > 0 && this.availableItemsForTemplate.every(item => item.selected);\n  }\n  // 更新選中項目的單價和單位\n  updateItemPrice(item, price) {\n    // 確保價格為有效數字且大於0\n    if (isNaN(price) || price < 0) {\n      price = 0;\n    }\n    item.CUnitPrice = price;\n    // 同步更新 availableItemsForTemplate 中對應項目的單價\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.CUnitPrice = price;\n    }\n  }\n  // 處理單價變更事件\n  onPriceChange(item, value) {\n    // 確保值為數字類型\n    const numericValue = typeof value === 'string' ? parseFloat(value) : value;\n    item.CUnitPrice = isNaN(numericValue) ? 0 : numericValue;\n  }\n  updateItemUnit(item, unit) {\n    // 清理單位字串\n    unit = unit ? unit.trim() : '';\n    item.CUnit = unit;\n    // 同步更新 availableItemsForTemplate 中對應項目的單位\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\n    if (availableItem) {\n      availableItem.CUnit = unit;\n    }\n  }\n  // 處理空間選擇變更\n  onSpaceSelectionChange(selectedSpaces) {\n    this.selectedSpacesForTemplate = selectedSpaces.map(space => ({\n      CSpaceID: space.CSpaceID,\n      CPart: space.CPart,\n      CLocation: space.CLocation,\n      selected: true\n    }));\n  }\n  // 處理項目選擇變更\n  onItemSelectionChange(selectedItems) {\n    this.selectedItemsForTemplate = selectedItems.map(item => ({\n      CSpaceID: item.CSpaceID,\n      CPart: item.CPart,\n      CLocation: item.CLocation,\n      selected: true,\n      CUnitPrice: 0,\n      // 預設為0，強制用戶輸入\n      CUnit: '' // 預設為空，強制用戶輸入\n    }));\n  }\n  // 統一的選擇變更處理方法\n  onSelectionChange(selectedItems) {\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\n      this.onSpaceSelectionChange(selectedItems);\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\n      this.onItemSelectionChange(selectedItems);\n    }\n  }\n  // 檢查項目是否有效（用於UI顯示）\n  isItemValid(item) {\n    // 檢核單價\n    if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\n      return false;\n    }\n    // 檢核單價是否為有效數字\n    if (isNaN(item.CUnitPrice)) {\n      return false;\n    }\n    // 檢核單位\n    if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\n      return false;\n    }\n    // 檢核單位長度\n    if (item.CUnit.trim().length > 10) {\n      return false;\n    }\n    return true;\n  }\n  // 獲取已完成設定的項目數量\n  getValidItemsCount() {\n    return this.selectedItemsForTemplate.filter(item => this.isItemValid(item)).length;\n  }\n  static {\n    this.ɵfac = function TemplateComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TemplateComponent)(i0.ɵɵdirectiveInject(i1.AllowHelper), i0.ɵɵdirectiveInject(i2.NbDialogService), i0.ɵɵdirectiveInject(i3.TemplateService), i0.ɵɵdirectiveInject(i3.SpaceService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.ValidationHelper));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TemplateComponent,\n      selectors: [[\"ngx-template\"]],\n      viewQuery: function TemplateComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.editModal = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateDetailModal = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 78,\n      vars: 15,\n      consts: [[\"templateModal\", \"\"], [\"templateDetailModal\", \"\"], [\"accent\", \"success\"], [1, \"alert\", \"alert-info\", \"mb-4\", 2, \"border-left\", \"4px solid #4a90e2\", \"background-color\", \"#f8f9ff\", \"border-radius\", \"6px\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-3\", 2, \"font-size\", \"1.2rem\"], [1, \"mb-0\", \"text-muted\", 2, \"font-size\", \"0.9rem\"], [1, \"d-flex\", \"flex-wrap\"], [1, \"col-md-6\"], [1, \"form-group\", \"d-flex\", \"align-items-center\", \"w-full\"], [\"for\", \"templateName\", 1, \"label\", \"col-3\"], [1, \"col-9\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u641C\\u5C0B\\u6A21\\u677F\\u540D\\u7A31...\", 1, \"w-full\", 3, \"ngModelChange\", \"keyup.enter\", \"ngModel\"], [\"for\", \"status\", 1, \"label\", \"col-3\"], [\"id\", \"status\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [3, \"value\"], [\"for\", \"templateType\", 1, \"label\", \"col-3\"], [\"id\", \"templateType\", \"placeholder\", \"\\u9078\\u64C7\\u6A21\\u677F\\u985E\\u578B...\", 3, \"ngModelChange\", \"selectedChange\", \"ngModel\"], [1, \"col-md-12\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-2\", \"mb-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"me-1\"], [1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"d-flex\", \"justify-content-end\", \"w-full\", \"mt-3\"], [\"class\", \"btn btn-info mx-1 btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", 2, \"min-width\", \"800px\"], [\"scope\", \"col\", 2, \"width\", \"120px\"], [\"scope\", \"col\", 2, \"width\", \"200px\"], [\"scope\", \"col\", 2, \"width\", \"100px\"], [\"scope\", \"col\", 2, \"width\", \"180px\"], [\"scope\", \"col\", 2, \"width\", \"140px\"], [4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\"], [3, \"PageChange\", \"Page\", \"PageSize\", \"CollectionSize\"], [1, \"btn\", \"btn-info\", \"mx-1\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-plus\", \"me-1\"], [1, \"badge\", 3, \"ngClass\"], [1, \"table-actions\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-eye\"], [\"class\", \"btn btn-outline-warning btn-sm me-1\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-outline-danger btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", \"me-1\", 3, \"click\"], [1, \"fas\", \"fa-edit\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-trash\"], [\"colspan\", \"5\", 1, \"text-muted\", \"py-4\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [2, \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"border-bottom\", \"py-3\", \"px-4\"], [1, \"mb-0\", \"text-primary\", \"font-weight-bold\"], [1, \"fas\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-ghost-light\", \"btn-sm\", \"rounded-circle\", 2, \"width\", \"32px\", \"height\", \"32px\", \"display\", \"flex\", \"align-items\", \"center\", \"justify-content\", \"center\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [1, \"px-4\", \"py-4\"], [1, \"row\"], [1, \"col-12\"], [1, \"form-group\", \"mb-4\"], [1, \"row\", \"mb-4\"], [1, \"d-flex\", \"align-items-center\", \"mb-3\"], [\"for\", \"templateName\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"flex-grow-1\", \"ms-2\"], [\"type\", \"text\", \"id\", \"templateName\", \"nbInput\", \"\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u6A21\\u677F\\u540D\\u7A31\", \"name\", \"templateName\", 1, \"form-control\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"border\", \"1px solid #e4e7ea\", 3, \"ngModelChange\", \"keydown.control.enter\", \"ngModel\"], [\"for\", \"templateStatus\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [1, \"w-full\"], [\"id\", \"templateStatus\", \"name\", \"templateStatus\", \"placeholder\", \"\\u9078\\u64C7\\u72C0\\u614B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-check-circle\", \"text-success\", \"me-2\"], [1, \"fas\", \"fa-times-circle\", \"text-danger\", \"me-2\"], [\"for\", \"templateType\", 1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"class\", \"form-control d-flex align-items-center\", \"style\", \"height: 42px; border-radius: 6px; background-color: #f8f9fa;\", 4, \"ngIf\"], [\"class\", \"w-full\", 4, \"ngIf\"], [1, \"col-md-12\", \"d-flex\", \"align-items-start\"], [1, \"required-field\", \"mb-0\", 2, \"min-width\", \"85px\", \"font-weight\", \"500\", \"padding-top\", \"8px\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [\"class\", \"selected-items-readonly\", 4, \"ngIf\"], [\"class\", \"selected-items-config\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"border-top\", \"pt-3\", \"px-4\", \"pb-3\", 2, \"background-color\", \"#f8f9fa\"], [1, \"btn\", \"btn-outline-secondary\", \"me-3\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-times\", \"me-1\"], [1, \"btn\", \"btn-primary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", \"background\", \"linear-gradient(135deg, #4a90e2 0%, #357abd 100%)\", 3, \"click\"], [1, \"fas\", \"fa-check\", \"me-1\"], [1, \"form-control\", \"d-flex\", \"align-items-center\", 2, \"height\", \"42px\", \"border-radius\", \"6px\", \"background-color\", \"#f8f9fa\"], [1, \"fas\", \"fa-info-circle\", \"text-info\", \"me-2\"], [\"id\", \"templateType\", \"name\", \"templateType\", \"placeholder\", \"\\u9078\\u64C7\\u985E\\u578B\", 2, \"height\", \"42px\", 3, \"ngModelChange\", \"ngModel\"], [1, \"fas\", \"fa-home\", \"text-primary\", \"me-2\"], [1, \"fas\", \"fa-list\", \"text-success\", \"me-2\"], [1, \"mb-3\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"existing-details-display\", 4, \"ngIf\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\", \"me-2\"], [1, \"text-muted\"], [1, \"existing-details-display\"], [1, \"alert\", \"alert-info\", \"mb-3\", 2, \"border-left\", \"4px solid #4a90e2\"], [1, \"fas\", \"fa-info-circle\", \"text-primary\", \"me-2\"], [\"class\", \"text-muted text-center py-3\", 4, \"ngIf\"], [1, \"text-muted\", \"text-center\", \"py-3\"], [1, \"fas\", \"fa-inbox\", \"me-2\"], [1, \"selected-items-readonly\"], [1, \"text-muted\", \"mb-3\"], [1, \"fas\", \"fa-list\", \"me-2\"], [1, \"badge\", \"badge-secondary\", \"ms-2\"], [\"class\", \"readonly-spaces\", 4, \"ngIf\"], [1, \"readonly-spaces\"], [\"class\", \"readonly-item mb-2 p-3 border rounded bg-light\", 4, \"ngFor\", \"ngForOf\"], [1, \"readonly-item\", \"mb-2\", \"p-3\", \"border\", \"rounded\", \"bg-light\"], [1, \"flex-grow-1\"], [1, \"item-name\", \"font-weight-bold\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-secondary\"], [1, \"item-location\", \"text-muted\", \"small\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-1\"], [1, \"badge\", \"badge-secondary\"], [1, \"fas\", \"fa-lock\", \"me-1\"], [3, \"selectionChange\", \"selectedItems\", \"multiple\"], [1, \"config-header\", \"mb-3\"], [1, \"mb-0\", \"text-secondary\", \"font-weight-bold\"], [1, \"fas\", \"fa-eye\", \"me-2\"], [1, \"config-items\"], [\"class\", \"config-item mb-3 p-3 border rounded bg-light\", 4, \"ngFor\", \"ngForOf\"], [1, \"config-item\", \"mb-3\", \"p-3\", \"border\", \"rounded\", \"bg-light\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-4\"], [1, \"item-info\"], [1, \"fas\", \"fa-cube\", \"me-2\", \"text-secondary\"], [1, \"col-md-3\"], [1, \"form-label\", \"small\", \"text-muted\", \"mb-1\"], [1, \"form-control\", \"bg-light\", 2, \"height\", \"38px\", \"display\", \"flex\", \"align-items\", \"center\"], [1, \"text-dark\"], [1, \"fas\", \"fa-dollar-sign\", \"ms-auto\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"ms-auto\", \"text-muted\"], [1, \"col-md-2\", \"text-center\"], [1, \"selected-items-config\"], [1, \"fas\", \"fa-cog\", \"me-2\"], [1, \"badge\", \"badge-info\", \"ms-2\"], [\"class\", \"config-item mb-3 p-3 border rounded\", 3, \"valid\", \"invalid\", 4, \"ngFor\", \"ngForOf\"], [1, \"config-summary\", \"mt-3\", \"p-3\", \"bg-light\", \"border\", \"rounded\"], [1, \"summary-item\"], [1, \"font-weight-bold\", \"text-primary\"], [1, \"font-weight-bold\"], [1, \"progress\", \"mt-2\", 2, \"height\", \"6px\"], [1, \"progress-bar\", \"bg-success\"], [1, \"config-item\", \"mb-3\", \"p-3\", \"border\", \"rounded\"], [1, \"fas\", \"fa-cube\", \"me-2\", \"text-primary\"], [1, \"text-danger\"], [\"type\", \"number\", \"nbInput\", \"\", \"size\", \"small\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u50F9\", \"min\", \"0\", \"step\", \"0.01\", 2, \"text-align\", \"right\", 3, \"ngModelChange\", \"ngModel\"], [\"nbSuffix\", \"\", \"icon\", \"dollar-sign-outline\", 1, \"text-success\"], [\"class\", \"invalid-feedback small\", 4, \"ngIf\"], [\"type\", \"text\", \"nbInput\", \"\", \"size\", \"small\", \"placeholder\", \"\\u8ACB\\u8F38\\u5165\\u55AE\\u4F4D\", \"maxlength\", \"10\", 3, \"ngModelChange\", \"ngModel\"], [\"nbSuffix\", \"\", \"icon\", \"tag-outline\", 1, \"text-info\"], [1, \"col-md-2\", \"text-end\"], [\"type\", \"button\", \"title\", \"\\u79FB\\u9664\\u6B64\\u9805\\u76EE\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"validation-status\", \"mt-2\"], [\"class\", \"text-success small\", 4, \"ngIf\"], [\"class\", \"text-warning small\", 4, \"ngIf\"], [1, \"invalid-feedback\", \"small\"], [1, \"text-success\", \"small\"], [1, \"fas\", \"fa-check-circle\", \"me-1\"], [1, \"text-warning\", \"small\"], [1, \"fas\", \"fa-exclamation-triangle\", \"me-1\"], [2, \"width\", \"800px\", \"max-height\", \"95vh\", \"border-radius\", \"12px\", \"box-shadow\", \"0 8px 32px rgba(0,0,0,0.12)\"], [1, \"fas\", \"fa-eye\", \"me-2\", \"text-info\"], [1, \"card\", \"mb-4\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"mb-0\", \"text-dark\", \"font-weight-bold\"], [1, \"fas\", \"fa-info-circle\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [1, \"form-group\", \"mb-3\"], [1, \"font-weight-bold\", \"text-muted\"], [1, \"fas\", \"fa-tag\", \"me-2\", \"text-primary\"], [1, \"mb-0\"], [1, \"fas\", \"fa-toggle-on\", \"me-2\", \"text-success\"], [1, \"me-1\"], [1, \"fas\", \"fa-calendar-plus\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-plus\", \"me-2\", \"text-warning\"], [1, \"fas\", \"fa-calendar-edit\", \"me-2\", \"text-info\"], [1, \"fas\", \"fa-user-edit\", \"me-2\", \"text-warning\"], [1, \"card\", 2, \"border\", \"1px solid #e4e7ea\", \"border-radius\", \"8px\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\", 2, \"background-color\", \"#f8f9fa\", \"border-bottom\", \"1px solid #e4e7ea\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-success\"], [1, \"badge\", \"badge-info\"], [1, \"btn\", \"btn-secondary\", \"px-4\", 2, \"min-width\", \"80px\", \"height\", \"38px\", \"border-radius\", \"6px\", 3, \"click\"], [1, \"fas\", \"fa-spinner\", \"fa-spin\", \"me-2\", \"text-primary\", 2, \"font-size\", \"1.2rem\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [1, \"table-responsive\"], [1, \"table\", \"table-sm\"], [\"scope\", \"col\", 1, \"col-1\"], [\"scope\", \"col\", 1, \"col-7\"], [\"scope\", \"col\", 1, \"col-4\"], [1, \"fas\", \"fa-home\", \"me-2\", \"text-muted\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\", \"text-muted\"], [1, \"text-center\", \"text-muted\", \"py-4\"]],\n      template: function TemplateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"nb-card\", 2)(1, \"nb-card-header\");\n          i0.ɵɵelement(2, \"ngx-breadcrumb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nb-card-body\")(4, \"div\", 3)(5, \"div\", 4);\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵelementStart(7, \"div\")(8, \"p\", 6);\n          i0.ɵɵtext(9, \" \\u5728\\u6B64\\u9801\\u9762\\u60A8\\u53EF\\u4EE5\\u7BA1\\u7406\\u7CFB\\u7D71\\u4E2D\\u7684\\u5404\\u500B\\u6A21\\u677F\\u8CC7\\u8A0A\\uFF0C\\u5305\\u62EC\\u65B0\\u589E\\u3001\\u7DE8\\u8F2F\\u3001\\u522A\\u9664\\u6A21\\u677F\\uFF0C\\u4EE5\\u53CA\\u8A2D\\u5B9A\\u6A21\\u677F\\u540D\\u7A31\\u3001\\u72C0\\u614B\\u548C\\u5305\\u542B\\u7684\\u7A7A\\u9593\\u7B49\\u3002 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"label\", 10);\n          i0.ɵɵtext(14, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"nb-form-field\", 11)(16, \"input\", 12);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_input_ngModelChange_16_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchKeyword, $event) || (ctx.searchKeyword = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"keyup.enter\", function TemplateComponent_Template_input_keyup_enter_16_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"div\", 8)(18, \"div\", 9)(19, \"label\", 13);\n          i0.ɵɵtext(20, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"nb-form-field\", 11)(22, \"nb-select\", 14);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_22_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchStatus, $event) || (ctx.searchStatus = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_22_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(23, \"nb-option\", 15);\n          i0.ɵɵtext(24, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"nb-option\", 15);\n          i0.ɵɵtext(26, \"\\u555F\\u7528\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"nb-option\", 15);\n          i0.ɵɵtext(28, \"\\u505C\\u7528\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(29, \"div\", 8)(30, \"div\", 9)(31, \"label\", 16);\n          i0.ɵɵtext(32, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"nb-form-field\", 11)(34, \"nb-select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function TemplateComponent_Template_nb_select_ngModelChange_34_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.searchTemplateType, $event) || (ctx.searchTemplateType = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"selectedChange\", function TemplateComponent_Template_nb_select_selectedChange_34_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelementStart(35, \"nb-option\", 15);\n          i0.ɵɵtext(36, \"\\u5168\\u90E8\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"nb-option\", 15);\n          i0.ɵɵtext(38, \"\\u7A7A\\u9593\\u6A21\\u677F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"nb-option\", 15);\n          i0.ɵɵtext(40, \"\\u9805\\u76EE\\u6A21\\u677F\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelement(41, \"div\", 8);\n          i0.ɵɵelementStart(42, \"div\", 18)(43, \"div\", 19)(44, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_44_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onReset());\n          });\n          i0.ɵɵelement(45, \"i\", 21);\n          i0.ɵɵtext(46, \"\\u91CD\\u7F6E \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 22);\n          i0.ɵɵlistener(\"click\", function TemplateComponent_Template_button_click_47_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSearch());\n          });\n          i0.ɵɵelement(48, \"i\", 23);\n          i0.ɵɵtext(49, \"\\u67E5\\u8A62 \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(50, \"div\", 18)(51, \"div\", 24);\n          i0.ɵɵtemplate(52, TemplateComponent_button_52_Template, 3, 0, \"button\", 25);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 26)(54, \"table\", 27)(55, \"thead\")(56, \"tr\")(57, \"th\", 28);\n          i0.ɵɵtext(58, \"\\u6A21\\u677F\\u985E\\u578B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 29);\n          i0.ɵɵtext(60, \"\\u6A21\\u677F\\u540D\\u7A31\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\", 30);\n          i0.ɵɵtext(62, \"\\u72C0\\u614B\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 31);\n          i0.ɵɵtext(64, \"\\u5EFA\\u7ACB\\u6642\\u9593\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 28);\n          i0.ɵɵtext(66, \"\\u5EFA\\u7ACB\\u8005\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 32);\n          i0.ɵɵtext(68, \"\\u64CD\\u4F5C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(69, \"tbody\");\n          i0.ɵɵtemplate(70, TemplateComponent_tr_70_Template, 19, 11, \"tr\", 33)(71, TemplateComponent_tr_71_Template, 4, 0, \"tr\", 34);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"nb-card-footer\", 35)(73, \"ngx-pagination\", 36);\n          i0.ɵɵtwoWayListener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.pageIndex, $event) || (ctx.pageIndex = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"PageChange\", function TemplateComponent_Template_ngx_pagination_PageChange_73_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChanged($event));\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(74, TemplateComponent_ng_template_74_Template, 61, 22, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(76, TemplateComponent_ng_template_76_Template, 75, 18, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchKeyword);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchStatus);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 0);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTemplateType);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"value\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", 2);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.isCreate);\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.templateList);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.templateList.length === 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtwoWayProperty(\"Page\", ctx.pageIndex);\n          i0.ɵɵproperty(\"PageSize\", ctx.pageSize)(\"CollectionSize\", ctx.totalRecords);\n        }\n      },\n      dependencies: [CommonModule, i6.NgClass, i6.NgForOf, i6.NgIf, i6.DecimalPipe, i6.DatePipe, SharedModule, i7.DefaultValueAccessor, i7.NumberValueAccessor, i7.NgControlStatus, i7.MaxLengthValidator, i7.MinValidator, i7.NgModel, i2.NbCardComponent, i2.NbCardBodyComponent, i2.NbCardFooterComponent, i2.NbCardHeaderComponent, i2.NbInputDirective, i2.NbSelectComponent, i2.NbOptionComponent, i2.NbFormFieldComponent, i2.NbSuffixDirective, i2.NbIconComponent, i8.BreadcrumbComponent, i9.PaginationComponent, SpacePickerComponent],\n      styles: [\"@charset \\\"UTF-8\\\";\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.btn-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .input-group-append[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-top-left-radius: 0;\\n  border-bottom-left-radius: 0;\\n}\\n\\nnb-card-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--color-fg-heading);\\n}\\n\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n.table-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  margin-right: 0;\\n}\\n\\n.badge.badge-success[_ngcontent-%COMP%] {\\n  background-color: #28a745;\\n  color: white;\\n}\\n.badge.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.badge.badge-info[_ngcontent-%COMP%] {\\n  background-color: #17a2b8;\\n  color: white;\\n}\\n.badge.badge-primary[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n}\\n\\n.required-field[_ngcontent-%COMP%]::after {\\n  content: \\\" *\\\";\\n  color: #dc3545;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  border-radius: 0.5rem;\\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\\n}\\n\\n.form-check-input[_ngcontent-%COMP%]:checked {\\n  background-color: #007bff;\\n  border-color: #007bff;\\n}\\n\\n.btn-close-white[_ngcontent-%COMP%] {\\n  filter: invert(1) grayscale(100%) brightness(200%);\\n}\\n\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset {\\n  border-bottom: 2px solid #f1f3f4;\\n  margin-bottom: 0;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab {\\n  padding: 0;\\n  margin-right: 8px;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link {\\n  padding: 14px 24px;\\n  border: none;\\n  border-radius: 8px 8px 0 0;\\n  background-color: transparent;\\n  color: #6c757d;\\n  font-weight: 500;\\n  font-size: 0.95rem;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link:hover {\\n  background-color: #f8f9fa;\\n  color: #495057;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active {\\n  background-color: #007bff;\\n  color: #fff;\\n  font-weight: 600;\\n  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab .tab-link.active::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  right: 0;\\n  height: 2px;\\n  background-color: #007bff;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content {\\n  padding: 0;\\n  border: none;\\n  background: transparent;\\n}\\n.template-type-tabs[_ngcontent-%COMP%]     nb-tabset .tabset .tab-content .tab-pane.active {\\n  display: block;\\n}\\n\\n.space-grid[_ngcontent-%COMP%] {\\n  display: grid !important;\\n  grid-template-columns: repeat(5, 1fr);\\n  gap: 16px;\\n  margin-bottom: 20px;\\n  width: 100%;\\n  border: 1px dashed #ccc;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  display: block;\\n  width: 100%;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  padding: 16px;\\n  border: 2px solid #e9ecef !important;\\n  border-radius: 12px;\\n  background-color: #fff !important;\\n  transition: all 0.3s ease;\\n  min-height: 80px;\\n  display: flex !important;\\n  flex-direction: column;\\n  justify-content: center;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\\n  position: relative;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2c3e50 !important;\\n  font-size: 0.95rem;\\n  line-height: 1.4;\\n  margin-bottom: 4px;\\n  text-align: center;\\n  display: block;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d !important;\\n  line-height: 1.3;\\n  font-weight: 400;\\n  text-align: center;\\n  display: block;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item[_ngcontent-%COMP%]:hover   .space-card[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background-color: #f8f9ff;\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%] {\\n  border-color: #007bff;\\n  background: linear-gradient(135deg, #e7f3ff 0%, #cce7ff 100%);\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-name[_ngcontent-%COMP%] {\\n  color: #0056b3;\\n  font-weight: 700;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]   .space-location[_ngcontent-%COMP%] {\\n  color: #495057;\\n}\\n.space-grid[_ngcontent-%COMP%]   .space-item.selected[_ngcontent-%COMP%]   .space-card[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 8px;\\n  right: 8px;\\n  width: 20px;\\n  height: 20px;\\n  background-color: #007bff;\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 12px;\\n  font-weight: bold;\\n}\\n\\n@media (max-width: 1200px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(4, 1fr);\\n  }\\n}\\n@media (max-width: 992px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(3, 1fr);\\n  }\\n}\\n@media (max-width: 768px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n    gap: 8px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .space-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-header[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e9ecef;\\n  padding-bottom: 12px;\\n  margin-bottom: 20px;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 1.1rem;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 4px 8px;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-header[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  line-height: 1.4;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border: 1px solid #e9ecef !important;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n  position: relative;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item.valid[_ngcontent-%COMP%] {\\n  border-color: #28a745 !important;\\n  background-color: #f8fff9;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item.valid[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 4px;\\n  background-color: #28a745;\\n  border-radius: 4px 0 0 4px;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item.invalid[_ngcontent-%COMP%] {\\n  border-color: #ffc107 !important;\\n  background-color: #fffbf0;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item.invalid[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  width: 4px;\\n  background-color: #ffc107;\\n  border-radius: 4px 0 0 4px;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  color: #2c3e50;\\n  margin-bottom: 4px;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-location[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .item-info[_ngcontent-%COMP%]   .item-location[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 6px;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]   .text-danger[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   nb-form-field[_ngcontent-%COMP%]   input.is-invalid[_ngcontent-%COMP%] {\\n  border-color: #dc3545;\\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .invalid-feedback[_ngcontent-%COMP%] {\\n  color: #dc3545;\\n  font-size: 0.8rem;\\n  margin-top: 4px;\\n  display: block;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  font-weight: 500;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   .text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   .text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107 !important;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .validation-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa !important;\\n  border: 1px solid #e9ecef !important;\\n  border-radius: 8px;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #6c757d !important;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .font-weight-bold[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .font-weight-bold.text-primary[_ngcontent-%COMP%] {\\n  color: #007bff !important;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .font-weight-bold.text-success[_ngcontent-%COMP%] {\\n  color: #28a745 !important;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   .font-weight-bold.text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107 !important;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n  border-radius: 3px;\\n  overflow: hidden;\\n}\\n.selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  transition: width 0.3s ease;\\n}\\n\\n@media (max-width: 768px) {\\n  .selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-4[_ngcontent-%COMP%], \\n   .selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], \\n   .selected-items-config[_ngcontent-%COMP%]   .config-items[_ngcontent-%COMP%]   .config-item[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-2[_ngcontent-%COMP%] {\\n    margin-bottom: 12px;\\n  }\\n  .selected-items-config[_ngcontent-%COMP%]   .config-summary[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["BaseComponent", "CommonModule", "SharedModule", "SpacePickerComponent", "tap", "EnumTemplateType", "EnumTemplateTypeHelper", "i0", "ɵɵelementStart", "ɵɵlistener", "TemplateComponent_button_52_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "templateModal_r4", "ɵɵreference", "ɵɵresetView", "openCreateModal", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "TemplateComponent_tr_70_button_17_Template_button_click_0_listener", "_r8", "template_r6", "$implicit", "openEditModal", "TemplateComponent_tr_70_button_18_Template_button_click_0_listener", "_r9", "deleteTemplate", "TemplateComponent_tr_70_Template_button_click_14_listener", "_r5", "templateDetailModal_r7", "viewTemplateDetail", "ɵɵtemplate", "TemplateComponent_tr_70_button_17_Template", "TemplateComponent_tr_70_button_18_Template", "ɵɵadvance", "ɵɵtextInterpolate1", "CTemplateType", "ɵɵtextInterpolate", "CTemplateName", "ɵɵproperty", "CStatus", "ɵɵpipeBind2", "CCreateDt", "CCreator", "isUpdate", "isDelete", "templateDetail", "SpaceTemplate", "ɵɵtwoWayListener", "TemplateComponent_ng_template_74_nb_form_field_40_Template_nb_select_ngModelChange_1_listener", "$event", "_r12", "ɵɵtwoWayBindingSet", "ɵɵtwoWayProperty", "ItemTemplate", "space_r13", "<PERSON>art", "CLocation", "TemplateComponent_ng_template_74_div_49_div_2_div_7_div_6_div_1_Template", "selectedSpacesForTemplate", "TemplateComponent_ng_template_74_div_49_div_2_div_7_div_6_Template", "length", "selectedItemsForTemplate", "TemplateComponent_ng_template_74_div_49_div_2_div_6_Template", "TemplateComponent_ng_template_74_div_49_div_2_div_7_Template", "TemplateComponent_ng_template_74_div_49_div_1_Template", "TemplateComponent_ng_template_74_div_49_div_2_Template", "isLoadingExistingDetails", "TemplateComponent_ng_template_74_div_50_Template_app_space_picker_selectionChange_1_listener", "_r14", "onSelectionChange", "item_r15", "CUnitPrice", "CUnit", "TemplateComponent_ng_template_74_div_52_div_10_Template", "TemplateComponent_ng_template_74_div_53_div_10_Template_input_ngModelChange_16_listener", "item_r17", "_r16", "onPriceChange", "TemplateComponent_ng_template_74_div_53_div_10_div_18_Template", "TemplateComponent_ng_template_74_div_53_div_10_Template_input_ngModelChange_25_listener", "TemplateComponent_ng_template_74_div_53_div_10_div_27_Template", "TemplateComponent_ng_template_74_div_53_div_10_Template_button_click_29_listener", "removeSelectedItem", "TemplateComponent_ng_template_74_div_53_div_10_div_32_Template", "TemplateComponent_ng_template_74_div_53_div_10_div_33_Template", "ɵɵclassProp", "isItemValid", "trim", "TemplateComponent_ng_template_74_div_53_div_10_Template", "ɵɵtextInterpolate2", "getValidItemsCount", "ɵɵstyleProp", "TemplateComponent_ng_template_74_Template_button_click_5_listener", "ref_r11", "_r10", "dialogRef", "onClose", "TemplateComponent_ng_template_74_Template_input_ngModelChange_17_listener", "TemplateComponent_ng_template_74_Template_input_keydown_control_enter_17_listener", "onSubmit", "TemplateComponent_ng_template_74_Template_nb_select_ngModelChange_24_listener", "TemplateComponent_ng_template_74_div_39_Template", "TemplateComponent_ng_template_74_nb_form_field_40_Template", "TemplateComponent_ng_template_74_div_49_Template", "TemplateComponent_ng_template_74_div_50_Template", "TemplateComponent_ng_template_74_div_52_Template", "TemplateComponent_ng_template_74_div_53_Template", "TemplateComponent_ng_template_74_Template_button_click_55_listener", "TemplateComponent_ng_template_74_Template_button_click_58_listener", "modalWidth", "isEditMode", "i_r21", "space_r20", "TemplateComponent_ng_template_76_div_70_div_1_tr_11_Template", "templateDetailSpaces", "TemplateComponent_ng_template_76_div_70_div_1_Template", "TemplateComponent_ng_template_76_div_70_div_2_Template", "TemplateComponent_ng_template_76_Template_button_click_5_listener", "ref_r19", "_r18", "TemplateComponent_ng_template_76_div_69_Template", "TemplateComponent_ng_template_76_div_70_Template", "TemplateComponent_ng_template_76_Template_button_click_72_listener", "selectedTemplateDetail", "ɵɵclassMap", "CUpdateDt", "CUpdator", "isLoadingTemplateDetail", "TemplateComponent", "constructor", "allow", "dialogService", "_templateService", "_spaceService", "message", "valid", "Math", "pageFirst", "pageSize", "pageIndex", "totalRecords", "templateList", "searchKeyword", "searchStatus", "searchTemplateType", "availableSpaces", "spaceSearchKeyword", "spaceSearchLocation", "spacePageIndex", "spacePageSize", "spaceTotalRecords", "allSpacesSelected", "availableItemsForTemplate", "itemSearchKeyword", "itemSearchLocation", "itemPageIndex", "itemPageSize", "itemTotalRecords", "allItemsSelected", "existingTemplateDetails", "ngOnInit", "loadTemplateList", "loadAvailableSpaces", "request", "PageIndex", "PageSize", "apiTemplateGetTemplateListPost$Json", "body", "pipe", "response", "StatusCode", "Entries", "map", "item", "CTemplateId", "TotalItems", "showErrorMSG", "Message", "subscribe", "apiSpaceGetSpaceListPost$Json", "CSpaceID", "selected", "some", "s", "updateAllSpacesSelectedState", "onSearch", "onReset", "loadAvailableItemsForTemplate", "find", "updateAllItemsSelectedState", "onSpaceSearch", "onSpaceReset", "onItemSearch", "onItemReset", "pageChanged", "page", "spacePageChanged", "itemPageChanged", "modal", "open", "context", "autoFocus", "template", "loadExistingTemplateDetails", "ref", "close", "validateTemplateForm", "updateTemplate", "createTemplate", "undefined", "isNaN", "details", "space", "CTemplateDetailId", "CReleateId", "templateData", "Details", "apiTemplateSaveTemplatePost$Json", "showSucessMSG", "confirm", "apiTemplateDeleteTemplatePost$Json", "templateId", "apiTemplateGetTemplateDetailByIdPost$Json", "toggleSpaceSelection", "push", "filter", "toggleAllSpaces", "for<PERSON>ach", "removeSelectedSpace", "availableSpace", "every", "toggleItemSelection", "newItem", "toggleAllItems", "availableItem", "updateItemPrice", "price", "value", "numericValue", "parseFloat", "updateItemUnit", "unit", "onSpaceSelectionChange", "selectedSpaces", "onItemSelectionChange", "selectedItems", "ɵɵdirectiveInject", "i1", "AllowHelper", "i2", "NbDialogService", "i3", "TemplateService", "SpaceService", "i4", "MessageService", "i5", "ValidationHelper", "selectors", "viewQuery", "TemplateComponent_Query", "rf", "ctx", "TemplateComponent_Template_input_ngModelChange_16_listener", "_r1", "TemplateComponent_Template_input_keyup_enter_16_listener", "TemplateComponent_Template_nb_select_ngModelChange_22_listener", "TemplateComponent_Template_nb_select_selectedChange_22_listener", "TemplateComponent_Template_nb_select_ngModelChange_34_listener", "TemplateComponent_Template_nb_select_selected<PERSON><PERSON>e_34_listener", "TemplateComponent_Template_button_click_44_listener", "TemplateComponent_Template_button_click_47_listener", "TemplateComponent_button_52_Template", "TemplateComponent_tr_70_Template", "TemplateComponent_tr_71_Template", "TemplateComponent_Template_ngx_pagination_PageChange_73_listener", "TemplateComponent_ng_template_74_Template", "ɵɵtemplateRefExtractor", "TemplateComponent_ng_template_76_Template", "isCreate", "i6", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "DatePipe", "i7", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "MaxLengthValidator", "MinValidator", "NgModel", "NbCardComponent", "NbCardBodyComponent", "NbCardFooterComponent", "NbCardHeaderComponent", "NbInputDirective", "NbSelectComponent", "NbOptionComponent", "NbFormFieldComponent", "NbSuffixDirective", "NbIconComponent", "i8", "BreadcrumbComponent", "i9", "PaginationComponent", "styles"], "sources": ["C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.ts", "C:\\Users\\<USER>\\Documents\\salechange-product\\adminFront\\src\\app\\pages\\system-management\\template\\template.component.html"], "sourcesContent": ["import { BaseComponent } from '../../components/base/baseComponent';\r\nimport { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { SharedModule } from '../../components/shared.module';\r\nimport { NbDialogService } from '@nebular/theme';\r\nimport { AllowHelper } from 'src/app/shared/helper/allowHelper';\r\nimport { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';\r\nimport { SpacePickerComponent, SpacePickerItem } from 'src/app/shared/components/space-picker/space-picker.component';\r\nimport { TemplateService, SpaceService } from 'src/services/api/services';\r\nimport { MessageService } from 'src/app/shared/services/message.service';\r\nimport { ValidationHelper } from 'src/app/shared/helper/validationHelper';\r\nimport { tap } from 'rxjs';\r\nimport {\r\n  SaveTemplateArgs,\r\n  GetTemplateDetailByIdArgs,\r\n  TemplateDetailItem,\r\n  GetSpaceListResponse\r\n} from 'src/services/api/models';\r\nimport { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';\r\n\r\nexport interface TemplateItem {\r\n  CTemplateId: number;\r\n  CTemplateName: string;\r\n  CTemplateType?: number;\r\n  CCreateDt: string;\r\n  CUpdateDt: string;\r\n  CCreator?: string | null;\r\n  CUpdator?: string | null;\r\n  CStatus?: number;\r\n  selected?: boolean;\r\n}\r\n\r\nexport interface SpacePickListItem {\r\n  CSpaceID: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n  selected?: boolean;\r\n}\r\n\r\n// 僅用於模板明細空間顯示\r\nexport interface TemplateDetailSpaceItem {\r\n  CReleateId: number;\r\n  CPart: string;\r\n  CLocation?: string | null;\r\n}\r\n\r\n// 項目模板選擇項目介面（擴展空間選擇項目，添加單價和單位）\r\nexport interface ItemPickListItem extends SpacePickListItem {\r\n  CUnitPrice?: number;\r\n  CUnit?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'ngx-template',\r\n  templateUrl: './template.component.html',\r\n  styleUrls: ['./template.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    SharedModule,\r\n    BreadcrumbComponent,\r\n    SpacePickerComponent\r\n  ],\r\n})\r\nexport class TemplateComponent extends BaseComponent implements OnInit {\r\n  Math = Math; // 讓模板可以使用 Math 函數\r\n  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉\r\n  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手\r\n\r\n  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;\r\n  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;\r\n  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;\r\n\r\n  constructor(\r\n    protected override allow: AllowHelper,\r\n    private dialogService: NbDialogService,\r\n    private _templateService: TemplateService,\r\n    private _spaceService: SpaceService,\r\n    private message: MessageService,\r\n    private valid: ValidationHelper\r\n  ) {\r\n    super(allow);\r\n  }\r\n\r\n  override pageFirst = 1;\r\n  override pageSize = 10;\r\n  override pageIndex = 1;\r\n  override totalRecords = 0;\r\n\r\n  // 模板相關屬性\r\n  templateList: TemplateItem[] = [];\r\n  templateDetail: SaveTemplateArgs = {};\r\n  searchKeyword: string = '';\r\n  searchStatus: number | null = null;\r\n  searchTemplateType: number | null = null;\r\n\r\n  // 空間選擇相關屬性\r\n  availableSpaces: SpacePickListItem[] = [];\r\n  selectedSpacesForTemplate: SpacePickListItem[] = [];\r\n  spaceSearchKeyword: string = '';\r\n  spaceSearchLocation: string = '';\r\n  spacePageIndex = 1;\r\n  spacePageSize = 10;\r\n  spaceTotalRecords = 0;\r\n  allSpacesSelected = false;\r\n\r\n  // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）\r\n  availableItemsForTemplate: ItemPickListItem[] = [];\r\n  selectedItemsForTemplate: ItemPickListItem[] = [];\r\n  itemSearchKeyword: string = '';\r\n  itemSearchLocation: string = '';\r\n  itemPageIndex = 1;\r\n  itemPageSize = 10;\r\n  itemTotalRecords = 0;\r\n  allItemsSelected = false;\r\n\r\n  // 模板明細相關屬性\r\n  selectedTemplateDetail: TemplateItem | null = null;\r\n  templateDetailSpaces: TemplateDetailSpaceItem[] = [];\r\n  isLoadingTemplateDetail = false;\r\n\r\n  // 編輯模式相關屬性\r\n  existingTemplateDetails: TemplateDetailItem[] = [];\r\n  isLoadingExistingDetails = false;\r\n\r\n  // 模態框模式控制\r\n  isEditMode = false;\r\n\r\n  override ngOnInit(): void {\r\n    this.loadTemplateList();\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 載入模板列表\r\n  loadTemplateList(): void {\r\n    const request = {\r\n      CTemplateName: this.searchKeyword || null,\r\n      CStatus: this.searchStatus,\r\n      CTemplateType: this.searchTemplateType,\r\n      PageIndex: this.pageIndex,\r\n      PageSize: this.pageSize\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.templateList = response.Entries?.map(item => ({\r\n            CTemplateId: item.CTemplateId!,\r\n            CTemplateName: item.CTemplateName!,\r\n            CTemplateType: item.CTemplateType, // 新增模板類型\r\n            CCreateDt: item.CCreateDt!,\r\n            CUpdateDt: item.CUpdateDt!,\r\n            CCreator: item.CCreator,\r\n            CUpdator: item.CUpdator,\r\n            CStatus: item.CStatus\r\n          })) || [];\r\n          this.totalRecords = response.TotalItems || 0;\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板列表失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 載入可用空間列表\r\n  loadAvailableSpaces(): void {\r\n    const request = {\r\n      CPart: this.spaceSearchKeyword || null,\r\n      CLocation: this.spaceSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.spacePageIndex,\r\n      PageSize: this.spacePageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableSpaces = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)\r\n          })) || [];\r\n          this.spaceTotalRecords = response.TotalItems || 0;\r\n          this.updateAllSpacesSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 搜尋功能\r\n  onSearch(): void {\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  onReset(): void {\r\n    this.searchKeyword = '';\r\n    this.searchStatus = null;\r\n    this.pageIndex = 1;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  // 載入項目模板可用項目（使用空間列表作為基礎）\r\n  loadAvailableItemsForTemplate(): void {\r\n    const request = {\r\n      CPart: this.itemSearchKeyword || null,\r\n      CLocation: this.itemSearchLocation || null,\r\n      CStatus: 1, // 只顯示啟用的空間\r\n      PageIndex: this.itemPageIndex,\r\n      PageSize: this.itemPageSize\r\n    };\r\n\r\n    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.availableItemsForTemplate = response.Entries?.map(item => ({\r\n            CSpaceID: item.CSpaceID!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation,\r\n            selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),\r\n            CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,\r\n            CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'\r\n          })) || [];\r\n          this.itemTotalRecords = response.TotalItems || 0;\r\n          this.updateAllItemsSelectedState();\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間搜尋功能\r\n  onSpaceSearch(): void {\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  onSpaceReset(): void {\r\n    this.spaceSearchKeyword = '';\r\n    this.spaceSearchLocation = '';\r\n    this.spacePageIndex = 1;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  // 項目搜尋功能\r\n  onItemSearch(): void {\r\n    this.itemPageIndex = 1;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  onItemReset(): void {\r\n    this.itemSearchKeyword = '';\r\n    this.itemSearchLocation = '';\r\n    this.itemPageIndex = 1;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  // 分頁功能\r\n  pageChanged(page: number): void {\r\n    this.pageIndex = page;\r\n    this.loadTemplateList();\r\n  }\r\n\r\n  spacePageChanged(page: number): void {\r\n    this.spacePageIndex = page;\r\n    this.loadAvailableSpaces();\r\n  }\r\n\r\n  itemPageChanged(page: number): void {\r\n    this.itemPageIndex = page;\r\n    this.loadAvailableItemsForTemplate();\r\n  }\r\n\r\n  // 模態框操作\r\n  openCreateModal(modal: TemplateRef<any>): void {\r\n    this.isEditMode = false;\r\n    this.templateDetail = {\r\n      CStatus: 1,\r\n      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板\r\n    };\r\n    this.selectedSpacesForTemplate = [];\r\n    this.selectedItemsForTemplate = [];\r\n    this.loadAvailableSpaces();\r\n    this.loadAvailableItemsForTemplate();\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {\r\n    this.isEditMode = true;\r\n    this.templateDetail = {\r\n      CTemplateId: template.CTemplateId,\r\n      CTemplateName: template.CTemplateName,\r\n      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,\r\n      CStatus: template.CStatus || 1\r\n    };\r\n\r\n    // 清空之前的選擇\r\n    this.selectedSpacesForTemplate = [];\r\n    this.selectedItemsForTemplate = [];\r\n    this.existingTemplateDetails = [];\r\n\r\n    // 載入已選擇的明細\r\n    this.loadExistingTemplateDetails(template.CTemplateId);\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n  }\r\n\r\n  onClose(ref: any): void {\r\n    ref.close();\r\n  }\r\n\r\n  // 計算模態框寬度\r\n  get modalWidth(): string {\r\n    return '800px';\r\n  }\r\n\r\n  onSubmit(ref: any): void {\r\n    if (!this.validateTemplateForm()) {\r\n      return;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateId) {\r\n      this.updateTemplate(ref);\r\n    } else {\r\n      this.createTemplate(ref);\r\n    }\r\n  }\r\n\r\n  // 驗證表單\r\n  validateTemplateForm(): boolean {\r\n    if (!this.templateDetail.CTemplateName?.trim()) {\r\n      this.message.showErrorMSG('請輸入模板名稱');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {\r\n      this.message.showErrorMSG('請選擇模板類型');\r\n      return false;\r\n    }\r\n\r\n    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {\r\n      this.message.showErrorMSG('請選擇模板狀態');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {\r\n      this.message.showErrorMSG('空間模板請至少選擇一個空間');\r\n      return false;\r\n    }\r\n\r\n    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {\r\n      this.message.showErrorMSG('項目模板請至少選擇一個項目');\r\n      return false;\r\n    }\r\n\r\n    // 驗證項目模板的單價和單位\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      for (const item of this.selectedItemsForTemplate) {\r\n        // 檢核單價\r\n        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單價是否為有效數字\r\n        if (isNaN(item.CUnitPrice)) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單位\r\n        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);\r\n          return false;\r\n        }\r\n\r\n        // 檢核單位長度\r\n        if (item.CUnit.trim().length > 10) {\r\n          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);\r\n          return false;\r\n        }\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 建立模板\r\n  createTemplate(ref: any): void {\r\n    let details: any[] = [];\r\n\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      // 空間模板的詳細資料\r\n      details = this.selectedSpacesForTemplate.map(space => ({\r\n        CTemplateDetailId: null,\r\n        CReleateId: space.CSpaceID,\r\n        CPart: space.CPart,\r\n        CLocation: space.CLocation\r\n      }));\r\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      // 項目模板的詳細資料，包含單價和單位\r\n      details = this.selectedItemsForTemplate.map(item => ({\r\n        CTemplateDetailId: null,\r\n        CReleateId: item.CSpaceID,\r\n        CPart: item.CPart,\r\n        CLocation: item.CLocation,\r\n        CUnitPrice: item.CUnitPrice,\r\n        CUnit: item.CUnit\r\n      }));\r\n    }\r\n\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus,\r\n      Details: details.length > 0 ? details : undefined\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('建立模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '建立模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 更新模板\r\n  updateTemplate(ref: any): void {\r\n    const templateData: SaveTemplateArgs = {\r\n      CTemplateId: this.templateDetail.CTemplateId,\r\n      CTemplateName: this.templateDetail.CTemplateName,\r\n      CTemplateType: this.templateDetail.CTemplateType,\r\n      CStatus: this.templateDetail.CStatus\r\n    };\r\n\r\n    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(\r\n      tap(response => {\r\n        if (response.StatusCode === 0) {\r\n          this.message.showSucessMSG('更新模板成功');\r\n          ref.close();\r\n          this.loadTemplateList();\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '更新模板失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n\r\n  // 刪除模板\r\n  deleteTemplate(template: TemplateItem): void {\r\n    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {\r\n      this._templateService.apiTemplateDeleteTemplatePost$Json({\r\n        body: { CTemplateId: template.CTemplateId }\r\n      }).pipe(\r\n        tap(response => {\r\n          if (response.StatusCode === 0) {\r\n            this.message.showSucessMSG('刪除模板成功');\r\n            this.loadTemplateList();\r\n          } else {\r\n            this.message.showErrorMSG(response.Message || '刪除模板失敗');\r\n          }\r\n        })\r\n      ).subscribe();\r\n    }\r\n  }\r\n\r\n  // 載入編輯模式下的已選擇明細\r\n  loadExistingTemplateDetails(templateId: number): void {\r\n    this.isLoadingExistingDetails = true;\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: templateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingExistingDetails = false;\r\n        if (response.StatusCode === 0) {\r\n          this.existingTemplateDetails = response.Entries || [];\r\n\r\n          // 根據模板類型設置已選擇的項目\r\n          if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\r\n            // 空間模板：設置已選擇的空間\r\n            this.selectedSpacesForTemplate = this.existingTemplateDetails.map(item => ({\r\n              CSpaceID: item.CReleateId!,\r\n              CPart: item.CPart!,\r\n              CLocation: item.CLocation,\r\n              selected: true\r\n            }));\r\n          } else {\r\n            // 項目模板：設置已選擇的項目（包含單價和單位）\r\n            this.selectedItemsForTemplate = this.existingTemplateDetails.map(item => ({\r\n              CSpaceID: item.CReleateId!,\r\n              CPart: item.CPart!,\r\n              CLocation: item.CLocation,\r\n              CUnitPrice: item.CUnitPrice || undefined,\r\n              CUnit: item.CUnit || undefined,\r\n              selected: true\r\n            }));\r\n          }\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 查看模板明細\r\n  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {\r\n    this.selectedTemplateDetail = template;\r\n    this.isLoadingTemplateDetail = true;\r\n    this.templateDetailSpaces = [];\r\n\r\n    this.dialogService.open(modal, {\r\n      context: {},\r\n      autoFocus: false\r\n    });\r\n\r\n    const request: GetTemplateDetailByIdArgs = {\r\n      templateId: template.CTemplateId\r\n    };\r\n\r\n    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(\r\n      tap(response => {\r\n        this.isLoadingTemplateDetail = false;\r\n        if (response.StatusCode === 0) {\r\n          this.templateDetailSpaces = response.Entries?.map(item => ({\r\n            CReleateId: item.CReleateId!,\r\n            CPart: item.CPart!,\r\n            CLocation: item.CLocation\r\n          })) || [];\r\n        } else {\r\n          this.message.showErrorMSG(response.Message || '載入模板明細失敗');\r\n        }\r\n      })\r\n    ).subscribe();\r\n  }\r\n\r\n  // 空間選擇相關方法\r\n  toggleSpaceSelection(space: SpacePickListItem): void {\r\n    space.selected = !space.selected;\r\n\r\n    if (space.selected) {\r\n      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n        this.selectedSpacesForTemplate.push({ ...space });\r\n      }\r\n    } else {\r\n      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  toggleAllSpaces(): void {\r\n    this.allSpacesSelected = !this.allSpacesSelected;\r\n\r\n    this.availableSpaces.forEach(space => {\r\n      space.selected = this.allSpacesSelected;\r\n      if (this.allSpacesSelected) {\r\n        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {\r\n          this.selectedSpacesForTemplate.push({ ...space });\r\n        }\r\n      } else {\r\n        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedSpace(space: SpacePickListItem): void {\r\n    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);\r\n\r\n    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);\r\n    if (availableSpace) {\r\n      availableSpace.selected = false;\r\n    }\r\n\r\n    this.updateAllSpacesSelectedState();\r\n  }\r\n\r\n  updateAllSpacesSelectedState(): void {\r\n    this.allSpacesSelected = this.availableSpaces.length > 0 &&\r\n      this.availableSpaces.every(space => space.selected);\r\n  }\r\n\r\n  // 項目選擇相關方法\r\n  toggleItemSelection(item: ItemPickListItem): void {\r\n    item.selected = !item.selected;\r\n\r\n    if (item.selected) {\r\n      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\r\n        // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\r\n        const newItem = {\r\n          ...item,\r\n          CUnitPrice: 0, // 設為0，強制用戶輸入\r\n          CUnit: '' // 設為空，強制用戶輸入\r\n        };\r\n        this.selectedItemsForTemplate.push(newItem);\r\n      }\r\n    } else {\r\n      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n    }\r\n\r\n    this.updateAllItemsSelectedState();\r\n  }\r\n\r\n  toggleAllItems(): void {\r\n    this.allItemsSelected = !this.allItemsSelected;\r\n\r\n    this.availableItemsForTemplate.forEach(item => {\r\n      item.selected = this.allItemsSelected;\r\n      if (this.allItemsSelected) {\r\n        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {\r\n          // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）\r\n          const newItem = {\r\n            ...item,\r\n            CUnitPrice: 0, // 設為0，強制用戶輸入\r\n            CUnit: '' // 設為空，強制用戶輸入\r\n          };\r\n          this.selectedItemsForTemplate.push(newItem);\r\n        }\r\n      } else {\r\n        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n      }\r\n    });\r\n  }\r\n\r\n  removeSelectedItem(item: ItemPickListItem): void {\r\n    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);\r\n\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.selected = false;\r\n    }\r\n\r\n    this.updateAllItemsSelectedState();\r\n  }\r\n\r\n  updateAllItemsSelectedState(): void {\r\n    this.allItemsSelected = this.availableItemsForTemplate.length > 0 &&\r\n      this.availableItemsForTemplate.every(item => item.selected);\r\n  }\r\n\r\n  // 更新選中項目的單價和單位\r\n  updateItemPrice(item: ItemPickListItem, price: number): void {\r\n    // 確保價格為有效數字且大於0\r\n    if (isNaN(price) || price < 0) {\r\n      price = 0;\r\n    }\r\n\r\n    item.CUnitPrice = price;\r\n    // 同步更新 availableItemsForTemplate 中對應項目的單價\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.CUnitPrice = price;\r\n    }\r\n  }\r\n\r\n  // 處理單價變更事件\r\n  onPriceChange(item: ItemPickListItem, value: any): void {\r\n    // 確保值為數字類型\r\n    const numericValue = typeof value === 'string' ? parseFloat(value) : value;\r\n    item.CUnitPrice = isNaN(numericValue) ? 0 : numericValue;\r\n  }\r\n\r\n  updateItemUnit(item: ItemPickListItem, unit: string): void {\r\n    // 清理單位字串\r\n    unit = unit ? unit.trim() : '';\r\n\r\n    item.CUnit = unit;\r\n    // 同步更新 availableItemsForTemplate 中對應項目的單位\r\n    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);\r\n    if (availableItem) {\r\n      availableItem.CUnit = unit;\r\n    }\r\n  }\r\n\r\n  // 處理空間選擇變更\r\n  onSpaceSelectionChange(selectedSpaces: SpacePickerItem[]): void {\r\n    this.selectedSpacesForTemplate = selectedSpaces.map(space => ({\r\n      CSpaceID: space.CSpaceID,\r\n      CPart: space.CPart,\r\n      CLocation: space.CLocation,\r\n      selected: true\r\n    }));\r\n  }\r\n\r\n  // 處理項目選擇變更\r\n  onItemSelectionChange(selectedItems: SpacePickerItem[]): void {\r\n    this.selectedItemsForTemplate = selectedItems.map(item => ({\r\n      CSpaceID: item.CSpaceID,\r\n      CPart: item.CPart,\r\n      CLocation: item.CLocation,\r\n      selected: true,\r\n      CUnitPrice: 0, // 預設為0，強制用戶輸入\r\n      CUnit: '' // 預設為空，強制用戶輸入\r\n    }));\r\n  }\r\n\r\n  // 統一的選擇變更處理方法\r\n  onSelectionChange(selectedItems: SpacePickerItem[]): void {\r\n    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {\r\n      this.onSpaceSelectionChange(selectedItems);\r\n    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {\r\n      this.onItemSelectionChange(selectedItems);\r\n    }\r\n  }\r\n\r\n  // 檢查項目是否有效（用於UI顯示）\r\n  isItemValid(item: ItemPickListItem): boolean {\r\n    // 檢核單價\r\n    if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {\r\n      return false;\r\n    }\r\n\r\n    // 檢核單價是否為有效數字\r\n    if (isNaN(item.CUnitPrice)) {\r\n      return false;\r\n    }\r\n\r\n    // 檢核單位\r\n    if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {\r\n      return false;\r\n    }\r\n\r\n    // 檢核單位長度\r\n    if (item.CUnit.trim().length > 10) {\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  // 獲取已完成設定的項目數量\r\n  getValidItemsCount(): number {\r\n    return this.selectedItemsForTemplate.filter(item => this.isItemValid(item)).length;\r\n  }\r\n}\r\n", "<nb-card accent=\"success\">\r\n  <nb-card-header>\r\n    <ngx-breadcrumb></ngx-breadcrumb>\r\n  </nb-card-header>\r\n  <nb-card-body>\r\n    <div class=\"alert alert-info mb-4\"\r\n      style=\"border-left: 4px solid #4a90e2; background-color: #f8f9ff; border-radius: 6px;\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <i class=\"fas fa-info-circle text-primary me-3\" style=\"font-size: 1.2rem;\"></i>\r\n        <div>\r\n          <p class=\"mb-0 text-muted\" style=\"font-size: 0.9rem;\">\r\n            在此頁面您可以管理系統中的各個模板資訊，包括新增、編輯、刪除模板，以及設定模板名稱、狀態和包含的空間等。\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 搜尋條件區域 -->\r\n    <div class=\"d-flex flex-wrap\">\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateName\" class=\"label col-3\">模板名稱</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <input type=\"text\" id=\"templateName\" nbInput class=\"w-full\" placeholder=\"搜尋模板名稱...\"\r\n              [(ngModel)]=\"searchKeyword\" (keyup.enter)=\"onSearch()\">\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"status\" class=\"label col-3\">狀態</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"status\" placeholder=\"選擇狀態...\" [(ngModel)]=\"searchStatus\" (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">啟用</nb-option>\r\n              <nb-option [value]=\"0\">停用</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <div class=\"form-group d-flex align-items-center w-full\">\r\n          <label for=\"templateType\" class=\"label col-3\">模板類型</label>\r\n          <nb-form-field class=\"col-9\">\r\n            <nb-select id=\"templateType\" placeholder=\"選擇模板類型...\" [(ngModel)]=\"searchTemplateType\"\r\n              (selectedChange)=\"onSearch()\">\r\n              <nb-option [value]=\"null\">全部</nb-option>\r\n              <nb-option [value]=\"1\">空間模板</nb-option>\r\n              <nb-option [value]=\"2\">項目模板</nb-option>\r\n            </nb-select>\r\n          </nb-form-field>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-6\">\r\n        <!-- 空白區域，讓查詢按鈕可以放到右下角 -->\r\n      </div>\r\n\r\n      <!-- 查詢和重置按鈕 -->\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-2 mb-3\">\r\n          <button class=\"btn btn-outline-secondary btn-sm me-2\" (click)=\"onReset()\">\r\n            <i class=\"fas fa-undo me-1\"></i>重置\r\n          </button>\r\n          <button class=\"btn btn-secondary btn-sm\" (click)=\"onSearch()\">\r\n            <i class=\"fas fa-search me-1\"></i>查詢\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-md-12\">\r\n        <div class=\"d-flex justify-content-end w-full mt-3\">\r\n          <button class=\"btn btn-info mx-1 btn-sm\" *ngIf=\"isCreate\" (click)=\"openCreateModal(templateModal)\">\r\n            <i class=\"fas fa-plus me-1\"></i>新增模板\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 模板列表表格 -->\r\n    <div class=\"table-responsive mt-4\">\r\n      <table class=\"table\" style=\"min-width: 800px;\">\r\n        <thead>\r\n          <tr>\r\n            <th scope=\"col\" style=\"width: 120px;\">模板類型</th>\r\n            <th scope=\"col\" style=\"width: 200px;\">模板名稱</th>\r\n            <th scope=\"col\" style=\"width: 100px;\">狀態</th>\r\n            <th scope=\"col\" style=\"width: 180px;\">建立時間</th>\r\n            <th scope=\"col\" style=\"width: 120px;\">建立者</th>\r\n            <th scope=\"col\" style=\"width: 140px;\">操作</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let template of templateList\">\r\n            <td>\r\n              {{ template.CTemplateType === 1 ? '空間模板' : (template.CTemplateType === 2 ? '項目模板' : '-') }}\r\n            </td>\r\n            <td>{{ template.CTemplateName }}</td>\r\n            <td>\r\n              <span class=\"badge\" [ngClass]=\"template.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                {{ template.CStatus === 1 ? '啟用' : '停用' }}\r\n              </span>\r\n            </td>\r\n            <td>{{ template.CCreateDt | date: 'yyyy/MM/dd HH:mm' }}</td>\r\n            <td>{{ template.CCreator || '-' }}</td>\r\n            <td class=\"table-actions\">\r\n              <button class=\"btn btn-outline-info btn-sm me-1\"\r\n                (click)=\"viewTemplateDetail(template, templateDetailModal)\">\r\n                <i class=\"fas fa-eye\"></i>查看\r\n              </button>\r\n              <button *ngIf=\"isUpdate\" class=\"btn btn-outline-warning btn-sm me-1\"\r\n                (click)=\"openEditModal(templateModal, template)\">\r\n                <i class=\"fas fa-edit\"></i>編輯\r\n              </button>\r\n              <button *ngIf=\"isDelete\" class=\"btn btn-outline-danger btn-sm\" (click)=\"deleteTemplate(template)\">\r\n                <i class=\"fas fa-trash\"></i>刪除\r\n              </button>\r\n            </td>\r\n          </tr>\r\n          <tr *ngIf=\"templateList.length === 0\">\r\n            <td colspan=\"5\" class=\"text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>目前沒有任何模板\r\n            </td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </nb-card-body>\r\n  <nb-card-footer class=\"d-flex justify-content-center\">\r\n    <ngx-pagination [(Page)]=\"pageIndex\" [PageSize]=\"pageSize\" [CollectionSize]=\"totalRecords\"\r\n      (PageChange)=\"pageChanged($event)\">\r\n    </ngx-pagination>\r\n  </nb-card-footer>\r\n</nb-card>\r\n\r\n<!-- 模板模態框（新增/編輯） -->\r\n<ng-template #templateModal let-ref=\"dialogRef\">\r\n  <nb-card [style.width]=\"modalWidth\"\r\n    style=\"max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas me-2\" [class.fa-plus-circle]=\"!isEditMode\" [class.fa-edit]=\"isEditMode\"\r\n          [class.text-success]=\"!isEditMode\" [class.text-warning]=\"isEditMode\"></i>\r\n        {{ isEditMode ? '編輯模板' : '新增模板' }}\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <div class=\"row\">\r\n        <!-- 模板名稱、狀態和類型 -->\r\n        <div class=\"col-12\">\r\n          <div class=\"form-group mb-4\">\r\n            <div class=\"row mb-4\">\r\n              <div class=\"col-md-6\">\r\n                <div class=\"d-flex align-items-center mb-3\">\r\n                  <label for=\"templateName\" class=\"required-field mb-0\"\r\n                    style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">模板名稱</label>\r\n                  <div class=\"flex-grow-1 ms-2\">\r\n                    <input type=\"text\" id=\"templateName\" class=\"form-control\" nbInput placeholder=\"請輸入模板名稱\"\r\n                      [(ngModel)]=\"templateDetail.CTemplateName\" name=\"templateName\"\r\n                      (keydown.control.enter)=\"onSubmit(ref)\"\r\n                      style=\"height: 42px; border-radius: 6px; border: 1px solid #e4e7ea;\" />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-md-6\">\r\n                <div class=\"d-flex align-items-center mb-3\">\r\n                  <label for=\"templateStatus\" class=\"required-field mb-0\"\r\n                    style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">狀態</label>\r\n                  <div class=\"flex-grow-1 ms-2\">\r\n                    <nb-form-field class=\"w-full\">\r\n                      <nb-select id=\"templateStatus\" [(ngModel)]=\"templateDetail.CStatus\" name=\"templateStatus\"\r\n                        placeholder=\"選擇狀態\" style=\"height: 42px;\">\r\n                        <nb-option [value]=\"1\">\r\n                          <span class=\"d-flex align-items-center\">\r\n                            <i class=\"fas fa-check-circle text-success me-2\"></i>啟用\r\n                          </span>\r\n                        </nb-option>\r\n                        <nb-option [value]=\"0\">\r\n                          <span class=\"d-flex align-items-center\">\r\n                            <i class=\"fas fa-times-circle text-danger me-2\"></i>停用\r\n                          </span>\r\n                        </nb-option>\r\n                      </nb-select>\r\n                    </nb-form-field>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <!-- 模板類型 -->\r\n            <div class=\"row mb-4\">\r\n              <div class=\"col-md-6\">\r\n                <div class=\"d-flex align-items-center\">\r\n                  <label for=\"templateType\" class=\"required-field mb-0\"\r\n                    style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">模板類型</label>\r\n                  <div class=\"flex-grow-1 ms-2\">\r\n                    <!-- 編輯模式：顯示當前類型，不可更改 -->\r\n                    <div *ngIf=\"isEditMode\" class=\"form-control d-flex align-items-center\"\r\n                      style=\"height: 42px; border-radius: 6px; background-color: #f8f9fa;\">\r\n                      <i class=\"fas fa-info-circle text-info me-2\"></i>\r\n                      <span>{{ templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate ? '空間模板' : '項目模板'\r\n                        }}</span>\r\n                    </div>\r\n                    <!-- 新增模式：可選擇類型 -->\r\n                    <nb-form-field *ngIf=\"!isEditMode\" class=\"w-full\">\r\n                      <nb-select id=\"templateType\" [(ngModel)]=\"templateDetail.CTemplateType\" name=\"templateType\"\r\n                        placeholder=\"選擇類型\" style=\"height: 42px;\">\r\n                        <nb-option [value]=\"EnumTemplateType.SpaceTemplate\">\r\n                          <span class=\"d-flex align-items-center\">\r\n                            <i class=\"fas fa-home text-primary me-2\"></i>空間模板\r\n                          </span>\r\n                        </nb-option>\r\n                        <nb-option [value]=\"EnumTemplateType.ItemTemplate\">\r\n                          <span class=\"d-flex align-items-center\">\r\n                            <i class=\"fas fa-list text-success me-2\"></i>項目模板\r\n                          </span>\r\n                        </nb-option>\r\n                      </nb-select>\r\n                    </nb-form-field>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"col-md-6\">\r\n                <!-- 空白區域，保持對齊 -->\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 明細項目選擇區域 -->\r\n      <div class=\"col-12\">\r\n        <div class=\"form-group mb-4\">\r\n          <div class=\"row\">\r\n            <div class=\"col-md-12 d-flex align-items-start\">\r\n              <label class=\"required-field mb-0\"\r\n                style=\"min-width: 85px; font-weight: 500; padding-top: 8px;\">明細項目</label>\r\n              <div class=\"flex-grow-1 ms-2\">\r\n                <!-- 編輯模式：顯示已選擇的明細（禁止異動） -->\r\n                <div *ngIf=\"isEditMode\" class=\"mb-3\">\r\n                  <div *ngIf=\"isLoadingExistingDetails\" class=\"text-center py-4\">\r\n                    <div class=\"spinner-border spinner-border-sm text-primary me-2\" role=\"status\"></div>\r\n                    <span class=\"text-muted\">載入已選擇明細...</span>\r\n                  </div>\r\n                  <div *ngIf=\"!isLoadingExistingDetails\" class=\"existing-details-display\">\r\n                    <div class=\"alert alert-info mb-3\" style=\"border-left: 4px solid #4a90e2;\">\r\n                      <div class=\"d-flex align-items-center\">\r\n                        <i class=\"fas fa-info-circle text-primary me-2\"></i>\r\n                        <span>編輯模式下無法異動明細項目，僅可修改模板基本資訊</span>\r\n                      </div>\r\n                    </div>\r\n                    <div *ngIf=\"selectedSpacesForTemplate.length === 0 && selectedItemsForTemplate.length === 0\"\r\n                      class=\"text-muted text-center py-3\">\r\n                      <i class=\"fas fa-inbox me-2\"></i>此模板尚無明細項目\r\n                    </div>\r\n                    <div *ngIf=\"selectedSpacesForTemplate.length > 0 || selectedItemsForTemplate.length > 0\"\r\n                      class=\"selected-items-readonly\">\r\n                      <h6 class=\"text-muted mb-3\">\r\n                        <i class=\"fas fa-list me-2\"></i>已選擇明細\r\n                        <span class=\"badge badge-secondary ms-2\">\r\n                          {{ (selectedSpacesForTemplate.length || selectedItemsForTemplate.length) }} 項\r\n                        </span>\r\n                      </h6>\r\n\r\n                      <!-- 空間模板：顯示已選擇的空間 -->\r\n                      <div\r\n                        *ngIf=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && selectedSpacesForTemplate.length > 0\"\r\n                        class=\"readonly-spaces\">\r\n                        <div *ngFor=\"let space of selectedSpacesForTemplate; let i = index\"\r\n                          class=\"readonly-item mb-2 p-3 border rounded bg-light\">\r\n                          <div class=\"d-flex align-items-center\">\r\n                            <div class=\"flex-grow-1\">\r\n                              <div class=\"item-name font-weight-bold\">\r\n                                <i class=\"fas fa-home me-2 text-secondary\"></i>{{ space.CPart }}\r\n                              </div>\r\n                              <div class=\"item-location text-muted small\">\r\n                                <i class=\"fas fa-map-marker-alt me-1\"></i>{{ space.CLocation || '-' }}\r\n                              </div>\r\n                            </div>\r\n                            <span class=\"badge badge-secondary\">\r\n                              <i class=\"fas fa-lock me-1\"></i>已鎖定\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 新增模式：正常的空間/項目選擇器 -->\r\n                <div *ngIf=\"!isEditMode\" class=\"mb-3\">\r\n                  <app-space-picker\r\n                    [selectedItems]=\"templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate ? selectedSpacesForTemplate : selectedItemsForTemplate\"\r\n                    [multiple]=\"true\" (selectionChange)=\"onSelectionChange($event)\">\r\n                  </app-space-picker>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-12\">\r\n              <!-- 編輯模式：顯示已選擇的明細項目（只讀） -->\r\n              <div\r\n                *ngIf=\"isEditMode && templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && selectedItemsForTemplate.length > 0\"\r\n                class=\"selected-items-readonly\">\r\n                <div class=\"config-header mb-3\">\r\n                  <h6 class=\"mb-0 text-secondary font-weight-bold\">\r\n                    <i class=\"fas fa-eye me-2\"></i>已選擇項目明細（只讀）\r\n                    <span class=\"badge badge-secondary ms-2\">{{ selectedItemsForTemplate.length }} 項</span>\r\n                  </h6>\r\n                  <small class=\"text-muted\">編輯模式下無法修改項目明細</small>\r\n                </div>\r\n                <div class=\"config-items\">\r\n                  <div *ngFor=\"let item of selectedItemsForTemplate; let i = index\"\r\n                    class=\"config-item mb-3 p-3 border rounded bg-light\">\r\n                    <div class=\"row align-items-center\">\r\n                      <!-- 項目資訊 -->\r\n                      <div class=\"col-md-4\">\r\n                        <div class=\"item-info\">\r\n                          <div class=\"item-name font-weight-bold\">\r\n                            <i class=\"fas fa-cube me-2 text-secondary\"></i>{{ item.CPart }}\r\n                          </div>\r\n                          <div class=\"item-location text-muted small\">\r\n                            <i class=\"fas fa-map-marker-alt me-1\"></i>{{ item.CLocation || '-' }}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 單價顯示 -->\r\n                      <div class=\"col-md-3\">\r\n                        <label class=\"form-label small text-muted mb-1\">單價</label>\r\n                        <div class=\"form-control bg-light\" style=\"height: 38px; display: flex; align-items: center;\">\r\n                          <span class=\"text-dark\">{{ item.CUnitPrice || 0 | number:'1.2-2' }}</span>\r\n                          <i class=\"fas fa-dollar-sign ms-auto text-muted\"></i>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 單位顯示 -->\r\n                      <div class=\"col-md-3\">\r\n                        <label class=\"form-label small text-muted mb-1\">單位</label>\r\n                        <div class=\"form-control bg-light\" style=\"height: 38px; display: flex; align-items: center;\">\r\n                          <span class=\"text-dark\">{{ item.CUnit || '-' }}</span>\r\n                          <i class=\"fas fa-tag ms-auto text-muted\"></i>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 狀態指示 -->\r\n                      <div class=\"col-md-2 text-center\">\r\n                        <span class=\"badge badge-secondary\">\r\n                          <i class=\"fas fa-lock me-1\"></i>已鎖定\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              <!-- 新增模式：項目模板的單價單位設定區域 -->\r\n              <div\r\n                *ngIf=\"!isEditMode && templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && selectedItemsForTemplate.length > 0\"\r\n                class=\"selected-items-config\">\r\n                <div class=\"config-header mb-3\">\r\n                  <h6 class=\"mb-0 text-primary font-weight-bold\">\r\n                    <i class=\"fas fa-cog me-2\"></i>項目單價單位設定\r\n                    <span class=\"badge badge-info ms-2\">{{ getValidItemsCount() }}/{{\r\n                      selectedItemsForTemplate.length }} 已完成</span>\r\n                  </h6>\r\n                  <small class=\"text-muted\">請為每個選中的項目設定單價和單位</small>\r\n                </div>\r\n                <div class=\"config-items\">\r\n                  <div *ngFor=\"let item of selectedItemsForTemplate; let i = index\"\r\n                    class=\"config-item mb-3 p-3 border rounded\" [class.valid]=\"isItemValid(item)\"\r\n                    [class.invalid]=\"!isItemValid(item)\">\r\n                    <div class=\"row align-items-center\">\r\n                      <!-- 項目資訊 -->\r\n                      <div class=\"col-md-4\">\r\n                        <div class=\"item-info\">\r\n                          <div class=\"item-name font-weight-bold\">\r\n                            <i class=\"fas fa-cube me-2 text-primary\"></i>{{ item.CPart }}\r\n                          </div>\r\n                          <div class=\"item-location text-muted small\">\r\n                            <i class=\"fas fa-map-marker-alt me-1\"></i>{{ item.CLocation || '-' }}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 單價設定 -->\r\n                      <div class=\"col-md-3\">\r\n                        <label class=\"form-label small text-muted mb-1\">單價 <span class=\"text-danger\">*</span></label>\r\n                        <nb-form-field>\r\n                          <input type=\"number\" nbInput size=\"small\" placeholder=\"請輸入單價\" [(ngModel)]=\"item.CUnitPrice\"\r\n                            (ngModelChange)=\"onPriceChange(item, $event)\" min=\"0\" step=\"0.01\"\r\n                            [class.is-invalid]=\"!item.CUnitPrice || item.CUnitPrice <= 0\" style=\"text-align: right;\" />\r\n                          <nb-icon nbSuffix icon=\"dollar-sign-outline\" class=\"text-success\"></nb-icon>\r\n                        </nb-form-field>\r\n                        <div *ngIf=\"!item.CUnitPrice || item.CUnitPrice <= 0\" class=\"invalid-feedback small\">\r\n                          單價必須大於0\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 單位設定 -->\r\n                      <div class=\"col-md-3\">\r\n                        <label class=\"form-label small text-muted mb-1\">單位 <span class=\"text-danger\">*</span></label>\r\n                        <nb-form-field>\r\n                          <input type=\"text\" nbInput size=\"small\" placeholder=\"請輸入單位\" [(ngModel)]=\"item.CUnit\"\r\n                            maxlength=\"10\"\r\n                            [class.is-invalid]=\"!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式'\" />\r\n                          <nb-icon nbSuffix icon=\"tag-outline\" class=\"text-info\"></nb-icon>\r\n                        </nb-form-field>\r\n                        <div *ngIf=\"!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式'\"\r\n                          class=\"invalid-feedback small\">\r\n                          請輸入具體單位（不能為空或\"式\"）\r\n                        </div>\r\n                      </div>\r\n\r\n                      <!-- 移除按鈕 -->\r\n                      <div class=\"col-md-2 text-end\">\r\n                        <button type=\"button\" class=\"btn btn-outline-danger btn-sm\" (click)=\"removeSelectedItem(item)\"\r\n                          title=\"移除此項目\">\r\n                          <i class=\"fas fa-trash\"></i>\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <!-- 驗證狀態指示 -->\r\n                    <div class=\"validation-status mt-2\">\r\n                      <div *ngIf=\"isItemValid(item)\" class=\"text-success small\">\r\n                        <i class=\"fas fa-check-circle me-1\"></i>設定完成\r\n                      </div>\r\n                      <div *ngIf=\"!isItemValid(item)\" class=\"text-warning small\">\r\n                        <i class=\"fas fa-exclamation-triangle me-1\"></i>請完成單價和單位設定\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <!-- 設定進度提示 -->\r\n                <div class=\"config-summary mt-3 p-3 bg-light border rounded\">\r\n                  <div class=\"row\">\r\n                    <div class=\"col-md-6\">\r\n                      <div class=\"summary-item\">\r\n                        <span class=\"text-muted\">已選擇項目：</span>\r\n                        <span class=\"font-weight-bold text-primary\">{{ selectedItemsForTemplate.length }} 個</span>\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"col-md-6\">\r\n                      <div class=\"summary-item\">\r\n                        <span class=\"text-muted\">已完成設定：</span>\r\n                        <span class=\"font-weight-bold\"\r\n                          [class.text-success]=\"getValidItemsCount() === selectedItemsForTemplate.length\"\r\n                          [class.text-warning]=\"getValidItemsCount() < selectedItemsForTemplate.length\">\r\n                          {{ getValidItemsCount() }} 個\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"progress mt-2\" style=\"height: 6px;\">\r\n                    <div class=\"progress-bar bg-success\"\r\n                      [style.width.%]=\"selectedItemsForTemplate.length > 0 ? (getValidItemsCount() / selectedItemsForTemplate.length) * 100 : 0\">\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-outline-secondary me-3 px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>取消\r\n      </button>\r\n      <button class=\"btn btn-primary px-4\" (click)=\"onSubmit(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px; background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);\">\r\n        <i class=\"fas fa-check me-1\"></i>{{ isEditMode ? '更新模板' : '建立模板' }}\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>\r\n\r\n\r\n\r\n<!-- 查看模板明細模態框 -->\r\n<ng-template #templateDetailModal let-ref=\"dialogRef\">\r\n  <nb-card style=\"width: 800px; max-height: 95vh; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.12);\">\r\n    <nb-card-header class=\"d-flex justify-content-between align-items-center border-bottom py-3 px-4\">\r\n      <h5 class=\"mb-0 text-primary font-weight-bold\">\r\n        <i class=\"fas fa-eye me-2 text-info\"></i>模板明細\r\n      </h5>\r\n      <button type=\"button\" class=\"btn btn-ghost-light btn-sm rounded-circle\" (click)=\"onClose(ref)\"\r\n        style=\"width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;\">\r\n        <i class=\"fas fa-times\"></i>\r\n      </button>\r\n    </nb-card-header>\r\n\r\n    <nb-card-body class=\"px-4 py-4\">\r\n      <!-- 模板基本資訊 -->\r\n      <div class=\"card mb-4\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header\" style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-info-circle me-2 text-primary\"></i>基本資訊\r\n          </h6>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-tag me-2 text-primary\"></i>模板名稱\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CTemplateName || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-toggle-on me-2 text-success\"></i>狀態\r\n                </label>\r\n                <p class=\"mb-0\">\r\n                  <span class=\"badge\"\r\n                    [ngClass]=\"selectedTemplateDetail?.CStatus === 1 ? 'badge-success' : 'badge-secondary'\">\r\n                    <i [class]=\"selectedTemplateDetail?.CStatus === 1 ? 'fas fa-check-circle' : 'fas fa-times-circle'\"\r\n                      class=\"me-1\"></i>\r\n                    {{ selectedTemplateDetail?.CStatus === 1 ? '啟用' : '停用' }}\r\n                  </span>\r\n                </p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-plus me-2 text-info\"></i>建立時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CCreateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-plus me-2 text-warning\"></i>建立者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CCreator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-calendar-edit me-2 text-info\"></i>更新時間\r\n                </label>\r\n                <p class=\"mb-0\">{{ (selectedTemplateDetail?.CUpdateDt | date: 'yyyy/MM/dd HH:mm') || '-' }}</p>\r\n              </div>\r\n            </div>\r\n            <div class=\"col-md-6\">\r\n              <div class=\"form-group mb-3\">\r\n                <label class=\"font-weight-bold text-muted\">\r\n                  <i class=\"fas fa-user-edit me-2 text-warning\"></i>更新者\r\n                </label>\r\n                <p class=\"mb-0\">{{ selectedTemplateDetail?.CUpdator || '-' }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 包含的空間列表 -->\r\n      <div class=\"card\" style=\"border: 1px solid #e4e7ea; border-radius: 8px;\">\r\n        <div class=\"card-header d-flex justify-content-between align-items-center\"\r\n          style=\"background-color: #f8f9fa; border-bottom: 1px solid #e4e7ea;\">\r\n          <h6 class=\"mb-0 text-dark font-weight-bold\">\r\n            <i class=\"fas fa-home me-2 text-success\"></i>包含的空間\r\n          </h6>\r\n          <span class=\"badge badge-info\">共 {{ templateDetailSpaces.length }} 個空間</span>\r\n        </div>\r\n        <div class=\"card-body\">\r\n          <!-- Loading 狀態 -->\r\n          <div *ngIf=\"isLoadingTemplateDetail\" class=\"text-center py-4\">\r\n            <i class=\"fas fa-spinner fa-spin me-2 text-primary\" style=\"font-size: 1.2rem;\"></i>\r\n            <span class=\"text-muted\">載入中...</span>\r\n          </div>\r\n\r\n          <!-- 空間列表 -->\r\n          <div *ngIf=\"!isLoadingTemplateDetail\">\r\n            <div class=\"table-responsive\" *ngIf=\"templateDetailSpaces.length > 0\">\r\n              <table class=\"table table-sm\">\r\n                <thead>\r\n                  <tr>\r\n                    <th scope=\"col\" class=\"col-1\">#</th>\r\n                    <th scope=\"col\" class=\"col-7\">項目名稱</th>\r\n                    <th scope=\"col\" class=\"col-4\">所屬區域</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  <tr *ngFor=\"let space of templateDetailSpaces; let i = index\">\r\n                    <td>{{ i + 1 }}</td>\r\n                    <td>\r\n                      <i class=\"fas fa-home me-2 text-muted\"></i>{{ space.CPart }}\r\n                    </td>\r\n                    <td>\r\n                      <i class=\"fas fa-map-marker-alt me-2 text-muted\"></i>{{ space.CLocation || '-' }}\r\n                    </td>\r\n                  </tr>\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n\r\n            <!-- 沒有空間時的提示 -->\r\n            <div *ngIf=\"templateDetailSpaces.length === 0\" class=\"text-center text-muted py-4\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>此模板尚未包含任何空間\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </nb-card-body>\r\n\r\n    <nb-card-footer class=\"d-flex justify-content-end border-top pt-3 px-4 pb-3\" style=\"background-color: #f8f9fa;\">\r\n      <button class=\"btn btn-secondary px-4\" (click)=\"onClose(ref)\"\r\n        style=\"min-width: 80px; height: 38px; border-radius: 6px;\">\r\n        <i class=\"fas fa-times me-1\"></i>關閉\r\n      </button>\r\n    </nb-card-footer>\r\n  </nb-card>\r\n</ng-template>"], "mappings": "AAAA,SAASA,aAAa,QAAQ,qCAAqC;AAEnE,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,gCAAgC;AAI7D,SAASC,oBAAoB,QAAyB,+DAA+D;AAIrH,SAASC,GAAG,QAAQ,MAAM;AAO1B,SAASC,gBAAgB,EAAEC,sBAAsB,QAAQ,sCAAsC;;;;;;;;;;;;;;;;;ICwDrFC,EAAA,CAAAC,cAAA,iBAAmG;IAAzCD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,gBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAK,eAAA,CAAAH,gBAAA,CAA8B;IAAA,EAAC;IAChGR,EAAA,CAAAY,SAAA,YAAgC;IAAAZ,EAAA,CAAAa,MAAA,gCAClC;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAoCLd,EAAA,CAAAC,cAAA,iBACmD;IAAjDD,EAAA,CAAAE,UAAA,mBAAAa,mEAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,gBAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAa,aAAA,CAAAX,gBAAA,EAAAS,WAAA,CAAsC;IAAA,EAAC;IAChDjB,EAAA,CAAAY,SAAA,YAA2B;IAAAZ,EAAA,CAAAa,MAAA,oBAC7B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IACTd,EAAA,CAAAC,cAAA,iBAAkG;IAAnCD,EAAA,CAAAE,UAAA,mBAAAkB,mEAAA;MAAApB,EAAA,CAAAI,aAAA,CAAAiB,GAAA;MAAA,MAAAJ,WAAA,GAAAjB,EAAA,CAAAO,aAAA,GAAAW,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAgB,cAAA,CAAAL,WAAA,CAAwB;IAAA,EAAC;IAC/FjB,EAAA,CAAAY,SAAA,YAA4B;IAAAZ,EAAA,CAAAa,MAAA,oBAC9B;IAAAb,EAAA,CAAAc,YAAA,EAAS;;;;;;IAtBXd,EADF,CAAAC,cAAA,SAA0C,SACpC;IACFD,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAA4B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEnCd,EADF,CAAAC,cAAA,SAAI,eAC2F;IAC3FD,EAAA,CAAAa,MAAA,GACF;IACFb,EADE,CAAAc,YAAA,EAAO,EACJ;IACLd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,GAAmD;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC5Dd,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAa,MAAA,IAA8B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAErCd,EADF,CAAAC,cAAA,cAA0B,kBAEsC;IAA5DD,EAAA,CAAAE,UAAA,mBAAAqB,0DAAA;MAAA,MAAAN,WAAA,GAAAjB,EAAA,CAAAI,aAAA,CAAAoB,GAAA,EAAAN,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAkB,sBAAA,GAAAzB,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAoB,kBAAA,CAAAT,WAAA,EAAAQ,sBAAA,CAAiD;IAAA,EAAC;IAC3DzB,EAAA,CAAAY,SAAA,aAA0B;IAAAZ,EAAA,CAAAa,MAAA,qBAC5B;IAAAb,EAAA,CAAAc,YAAA,EAAS;IAKTd,EAJA,CAAA2B,UAAA,KAAAC,0CAAA,qBACmD,KAAAC,0CAAA,qBAG+C;IAItG7B,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAvBDd,EAAA,CAAA8B,SAAA,GACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAAd,WAAA,CAAAe,aAAA,sCAAAf,WAAA,CAAAe,aAAA,+CACF;IACIhC,EAAA,CAAA8B,SAAA,GAA4B;IAA5B9B,EAAA,CAAAiC,iBAAA,CAAAhB,WAAA,CAAAiB,aAAA,CAA4B;IAEVlC,EAAA,CAAA8B,SAAA,GAAwE;IAAxE9B,EAAA,CAAAmC,UAAA,YAAAlB,WAAA,CAAAmB,OAAA,6CAAwE;IAC1FpC,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAAd,WAAA,CAAAmB,OAAA,8CACF;IAEEpC,EAAA,CAAA8B,SAAA,GAAmD;IAAnD9B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAqC,WAAA,QAAApB,WAAA,CAAAqB,SAAA,sBAAmD;IACnDtC,EAAA,CAAA8B,SAAA,GAA8B;IAA9B9B,EAAA,CAAAiC,iBAAA,CAAAhB,WAAA,CAAAsB,QAAA,QAA8B;IAMvBvC,EAAA,CAAA8B,SAAA,GAAc;IAAd9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAkC,QAAA,CAAc;IAIdxC,EAAA,CAAA8B,SAAA,EAAc;IAAd9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAmC,QAAA,CAAc;;;;;IAMzBzC,EADF,CAAAC,cAAA,SAAsC,aACI;IACtCD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,wDACzC;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IA+EKd,EAAA,CAAAC,cAAA,cACuE;IACrED,EAAA,CAAAY,SAAA,YAAiD;IACjDZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,GACF;IACNb,EADM,CAAAc,YAAA,EAAO,EACP;;;;IAFEd,EAAA,CAAA8B,SAAA,GACF;IADE9B,EAAA,CAAAiC,iBAAA,CAAA3B,MAAA,CAAAoC,cAAA,CAAAV,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAA6C,aAAA,2DACF;;;;;;IAIJ3C,EADF,CAAAC,cAAA,wBAAkD,oBAEL;IADdD,EAAA,CAAA4C,gBAAA,2BAAAC,8FAAAC,MAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAAzC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAgD,kBAAA,CAAA1C,MAAA,CAAAoC,cAAA,CAAAV,aAAA,EAAAc,MAAA,MAAAxC,MAAA,CAAAoC,cAAA,CAAAV,aAAA,GAAAc,MAAA;MAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;IAAA,EAA0C;IAGnE9C,EADF,CAAAC,cAAA,oBAAoD,cACV;IACtCD,EAAA,CAAAY,SAAA,YAA6C;IAAAZ,EAAA,CAAAa,MAAA,gCAC/C;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,oBAAmD,cACT;IACtCD,EAAA,CAAAY,SAAA,YAA6C;IAAAZ,EAAA,CAAAa,MAAA,gCAC/C;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACG,EACF,EACE;;;;IAbed,EAAA,CAAA8B,SAAA,EAA0C;IAA1C9B,EAAA,CAAAiD,gBAAA,YAAA3C,MAAA,CAAAoC,cAAA,CAAAV,aAAA,CAA0C;IAE1DhC,EAAA,CAAA8B,SAAA,EAAwC;IAAxC9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAR,gBAAA,CAAA6C,aAAA,CAAwC;IAKxC3C,EAAA,CAAA8B,SAAA,GAAuC;IAAvC9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAR,gBAAA,CAAAoD,YAAA,CAAuC;;;;;IA4BxDlD,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAY,SAAA,cAAoF;IACpFZ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAa,MAAA,oDAAU;IACrCb,EADqC,CAAAc,YAAA,EAAO,EACtC;;;;;IAQJd,EAAA,CAAAC,cAAA,cACsC;IACpCD,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,8DACnC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IAkBId,EAJN,CAAAC,cAAA,eACyD,aAChB,eACZ,eACiB;IACtCD,EAAA,CAAAY,SAAA,aAA+C;IAAAZ,EAAA,CAAAa,MAAA,GACjD;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,eAA4C;IAC1CD,EAAA,CAAAY,SAAA,aAA0C;IAAAZ,EAAA,CAAAa,MAAA,GAC5C;IACFb,EADE,CAAAc,YAAA,EAAM,EACF;IACNd,EAAA,CAAAC,cAAA,gBAAoC;IAClCD,EAAA,CAAAY,SAAA,cAAgC;IAAAZ,EAAA,CAAAa,MAAA,2BAClC;IAEJb,EAFI,CAAAc,YAAA,EAAO,EACH,EACF;;;;IAViDd,EAAA,CAAA8B,SAAA,GACjD;IADiD9B,EAAA,CAAA+B,kBAAA,KAAAoB,SAAA,CAAAC,KAAA,MACjD;IAE4CpD,EAAA,CAAA8B,SAAA,GAC5C;IAD4C9B,EAAA,CAAA+B,kBAAA,KAAAoB,SAAA,CAAAE,SAAA,aAC5C;;;;;IAZRrD,EAAA,CAAAC,cAAA,eAE0B;IACxBD,EAAA,CAAA2B,UAAA,IAAA2B,wEAAA,oBACyD;IAe3DtD,EAAA,CAAAc,YAAA,EAAM;;;;IAhBmBd,EAAA,CAAA8B,SAAA,EAA8B;IAA9B9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAiD,yBAAA,CAA8B;;;;;IAXvDvD,EAFF,CAAAC,cAAA,eACkC,cACJ;IAC1BD,EAAA,CAAAY,SAAA,aAAgC;IAAAZ,EAAA,CAAAa,MAAA,sCAChC;IAAAb,EAAA,CAAAC,cAAA,gBAAyC;IACvCD,EAAA,CAAAa,MAAA,GACF;IACFb,EADE,CAAAc,YAAA,EAAO,EACJ;IAGLd,EAAA,CAAA2B,UAAA,IAAA6B,kEAAA,mBAE0B;IAkB5BxD,EAAA,CAAAc,YAAA,EAAM;;;;IAzBAd,EAAA,CAAA8B,SAAA,GACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAAzB,MAAA,CAAAiD,yBAAA,CAAAE,MAAA,IAAAnD,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,aACF;IAKCzD,EAAA,CAAA8B,SAAA,EAA6G;IAA7G9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAoC,cAAA,CAAAV,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAA6C,aAAA,IAAArC,MAAA,CAAAiD,yBAAA,CAAAE,MAAA,KAA6G;;;;;IApBhHzD,EAFJ,CAAAC,cAAA,cAAwE,cACK,aAClC;IACrCD,EAAA,CAAAY,SAAA,YAAoD;IACpDZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAa,MAAA,uJAAwB;IAElCb,EAFkC,CAAAc,YAAA,EAAO,EACjC,EACF;IAKNd,EAJA,CAAA2B,UAAA,IAAAgC,4DAAA,kBACsC,IAAAC,4DAAA,kBAIJ;IA8BpC5D,EAAA,CAAAc,YAAA,EAAM;;;;IAnCEd,EAAA,CAAA8B,SAAA,GAAqF;IAArF9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAiD,yBAAA,CAAAE,MAAA,UAAAnD,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,OAAqF;IAIrFzD,EAAA,CAAA8B,SAAA,EAAiF;IAAjF9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAiD,yBAAA,CAAAE,MAAA,QAAAnD,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,KAAiF;;;;;IAhB3FzD,EAAA,CAAAC,cAAA,cAAqC;IAKnCD,EAJA,CAAA2B,UAAA,IAAAkC,sDAAA,kBAA+D,IAAAC,sDAAA,kBAIS;IA2C1E9D,EAAA,CAAAc,YAAA,EAAM;;;;IA/CEd,EAAA,CAAA8B,SAAA,EAA8B;IAA9B9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAyD,wBAAA,CAA8B;IAI9B/D,EAAA,CAAA8B,SAAA,EAA+B;IAA/B9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAyD,wBAAA,CAA+B;;;;;;IA+CrC/D,EADF,CAAAC,cAAA,cAAsC,4BAG8B;IAA9CD,EAAA,CAAAE,UAAA,6BAAA8D,6FAAAlB,MAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAmBJ,MAAA,CAAA4D,iBAAA,CAAApB,MAAA,CAAyB;IAAA,EAAC;IAEnE9C,EADE,CAAAc,YAAA,EAAmB,EACf;;;;IAHFd,EAAA,CAAA8B,SAAA,EAAwI;IACxI9B,EADA,CAAAmC,UAAA,kBAAA7B,MAAA,CAAAoC,cAAA,CAAAV,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAA6C,aAAA,GAAArC,MAAA,CAAAiD,yBAAA,GAAAjD,MAAA,CAAAoD,wBAAA,CAAwI,kBACvH;;;;;IAwBX1D,EANR,CAAAC,cAAA,eACuD,eACjB,eAEZ,eACG,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAA+C;IAAAZ,EAAA,CAAAa,MAAA,GACjD;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,eAA4C;IAC1CD,EAAA,CAAAY,SAAA,aAA0C;IAAAZ,EAAA,CAAAa,MAAA,GAC5C;IAEJb,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;IAIJd,EADF,CAAAC,cAAA,gBAAsB,kBAC4B;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAExDd,EADF,CAAAC,cAAA,gBAA6F,iBACnE;IAAAD,EAAA,CAAAa,MAAA,IAA2C;;IAAAb,EAAA,CAAAc,YAAA,EAAO;IAC1Ed,EAAA,CAAAY,SAAA,cAAqD;IAEzDZ,EADE,CAAAc,YAAA,EAAM,EACF;IAIJd,EADF,CAAAC,cAAA,gBAAsB,kBAC4B;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAExDd,EADF,CAAAC,cAAA,gBAA6F,iBACnE;IAAAD,EAAA,CAAAa,MAAA,IAAuB;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACtDd,EAAA,CAAAY,SAAA,cAA6C;IAEjDZ,EADE,CAAAc,YAAA,EAAM,EACF;IAIJd,EADF,CAAAC,cAAA,gBAAkC,iBACI;IAClCD,EAAA,CAAAY,SAAA,cAAgC;IAAAZ,EAAA,CAAAa,MAAA,2BAClC;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACH,EACF,EACF;;;;IAjCmDd,EAAA,CAAA8B,SAAA,GACjD;IADiD9B,EAAA,CAAA+B,kBAAA,KAAAoC,QAAA,CAAAf,KAAA,MACjD;IAE4CpD,EAAA,CAAA8B,SAAA,GAC5C;IAD4C9B,EAAA,CAAA+B,kBAAA,KAAAoC,QAAA,CAAAd,SAAA,aAC5C;IAQwBrD,EAAA,CAAA8B,SAAA,GAA2C;IAA3C9B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAqC,WAAA,QAAA8B,QAAA,CAAAC,UAAA,gBAA2C;IAS3CpE,EAAA,CAAA8B,SAAA,GAAuB;IAAvB9B,EAAA,CAAAiC,iBAAA,CAAAkC,QAAA,CAAAE,KAAA,QAAuB;;;;;IAnCvDrE,EAJJ,CAAAC,cAAA,eAEkC,eACA,cACmB;IAC/CD,EAAA,CAAAY,SAAA,aAA+B;IAAAZ,EAAA,CAAAa,MAAA,0EAC/B;IAAAb,EAAA,CAAAC,cAAA,gBAAyC;IAAAD,EAAA,CAAAa,MAAA,GAAuC;IAClFb,EADkF,CAAAc,YAAA,EAAO,EACpF;IACLd,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAa,MAAA,qFAAa;IACzCb,EADyC,CAAAc,YAAA,EAAQ,EAC3C;IACNd,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAA2B,UAAA,KAAA2C,uDAAA,oBACuD;IAyC3DtE,EADE,CAAAc,YAAA,EAAM,EACF;;;;IA/CyCd,EAAA,CAAA8B,SAAA,GAAuC;IAAvC9B,EAAA,CAAA+B,kBAAA,KAAAzB,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,YAAuC;IAK5DzD,EAAA,CAAA8B,SAAA,GAA6B;IAA7B9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAoD,wBAAA,CAA6B;;;;;IAkF7C1D,EAAA,CAAAC,cAAA,eAAqF;IACnFD,EAAA,CAAAa,MAAA,8CACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IAYNd,EAAA,CAAAC,cAAA,eACiC;IAC/BD,EAAA,CAAAa,MAAA,uGACF;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IAcRd,EAAA,CAAAC,cAAA,eAA0D;IACxDD,EAAA,CAAAY,SAAA,aAAwC;IAAAZ,EAAA,CAAAa,MAAA,gCAC1C;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IACNd,EAAA,CAAAC,cAAA,eAA2D;IACzDD,EAAA,CAAAY,SAAA,aAAgD;IAAAZ,EAAA,CAAAa,MAAA,oEAClD;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;;IAtDFd,EAPR,CAAAC,cAAA,eAEuC,eACD,eAEZ,eACG,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAA6C;IAAAZ,EAAA,CAAAa,MAAA,GAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,eAA4C;IAC1CD,EAAA,CAAAY,SAAA,aAA0C;IAAAZ,EAAA,CAAAa,MAAA,GAC5C;IAEJb,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;IAIJd,EADF,CAAAC,cAAA,gBAAsB,kBAC4B;IAAAD,EAAA,CAAAa,MAAA,qBAAG;IAAAb,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAa,MAAA,SAAC;IAAOb,EAAP,CAAAc,YAAA,EAAO,EAAQ;IAE3Fd,EADF,CAAAC,cAAA,qBAAe,kBAGgF;IAF/BD,EAAA,CAAA4C,gBAAA,2BAAA2B,wFAAAzB,MAAA;MAAA,MAAA0B,QAAA,GAAAxE,EAAA,CAAAI,aAAA,CAAAqE,IAAA,EAAAvD,SAAA;MAAAlB,EAAA,CAAAgD,kBAAA,CAAAwB,QAAA,CAAAJ,UAAA,EAAAtB,MAAA,MAAA0B,QAAA,CAAAJ,UAAA,GAAAtB,MAAA;MAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;IAAA,EAA6B;IACzF9C,EAAA,CAAAE,UAAA,2BAAAqE,wFAAAzB,MAAA;MAAA,MAAA0B,QAAA,GAAAxE,EAAA,CAAAI,aAAA,CAAAqE,IAAA,EAAAvD,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAiBJ,MAAA,CAAAoE,aAAA,CAAAF,QAAA,EAAA1B,MAAA,CAA2B;IAAA,EAAC;IAD/C9C,EAAA,CAAAc,YAAA,EAE6F;IAC7Fd,EAAA,CAAAY,SAAA,oBAA4E;IAC9EZ,EAAA,CAAAc,YAAA,EAAgB;IAChBd,EAAA,CAAA2B,UAAA,KAAAgD,8DAAA,mBAAqF;IAGvF3E,EAAA,CAAAc,YAAA,EAAM;IAIJd,EADF,CAAAC,cAAA,gBAAsB,kBAC4B;IAAAD,EAAA,CAAAa,MAAA,qBAAG;IAAAb,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAa,MAAA,SAAC;IAAOb,EAAP,CAAAc,YAAA,EAAO,EAAQ;IAE3Fd,EADF,CAAAC,cAAA,qBAAe,kBAGiF;IAFlCD,EAAA,CAAA4C,gBAAA,2BAAAgC,wFAAA9B,MAAA;MAAA,MAAA0B,QAAA,GAAAxE,EAAA,CAAAI,aAAA,CAAAqE,IAAA,EAAAvD,SAAA;MAAAlB,EAAA,CAAAgD,kBAAA,CAAAwB,QAAA,CAAAH,KAAA,EAAAvB,MAAA,MAAA0B,QAAA,CAAAH,KAAA,GAAAvB,MAAA;MAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;IAAA,EAAwB;IAApF9C,EAAA,CAAAc,YAAA,EAE8F;IAC9Fd,EAAA,CAAAY,SAAA,oBAAiE;IACnEZ,EAAA,CAAAc,YAAA,EAAgB;IAChBd,EAAA,CAAA2B,UAAA,KAAAkD,8DAAA,mBACiC;IAGnC7E,EAAA,CAAAc,YAAA,EAAM;IAIJd,EADF,CAAAC,cAAA,gBAA+B,mBAEb;IAD4CD,EAAA,CAAAE,UAAA,mBAAA4E,iFAAA;MAAA,MAAAN,QAAA,GAAAxE,EAAA,CAAAI,aAAA,CAAAqE,IAAA,EAAAvD,SAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAyE,kBAAA,CAAAP,QAAA,CAAwB;IAAA,EAAC;IAE5FxE,EAAA,CAAAY,SAAA,aAA4B;IAGlCZ,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;IAGNd,EAAA,CAAAC,cAAA,gBAAoC;IAIlCD,EAHA,CAAA2B,UAAA,KAAAqD,8DAAA,mBAA0D,KAAAC,8DAAA,mBAGC;IAI/DjF,EADE,CAAAc,YAAA,EAAM,EACF;;;;;IA7DJd,EAD4C,CAAAkF,WAAA,UAAA5E,MAAA,CAAA6E,WAAA,CAAAX,QAAA,EAAiC,aAAAlE,MAAA,CAAA6E,WAAA,CAAAX,QAAA,EACzC;IAMiBxE,EAAA,CAAA8B,SAAA,GAC/C;IAD+C9B,EAAA,CAAA+B,kBAAA,KAAAyC,QAAA,CAAApB,KAAA,MAC/C;IAE4CpD,EAAA,CAAA8B,SAAA,GAC5C;IAD4C9B,EAAA,CAAA+B,kBAAA,KAAAyC,QAAA,CAAAnB,SAAA,aAC5C;IAUErD,EAAA,CAAA8B,SAAA,GAA6D;IAA7D9B,EAAA,CAAAkF,WAAA,gBAAAV,QAAA,CAAAJ,UAAA,IAAAI,QAAA,CAAAJ,UAAA,MAA6D;IAFDpE,EAAA,CAAAiD,gBAAA,YAAAuB,QAAA,CAAAJ,UAAA,CAA6B;IAKvFpE,EAAA,CAAA8B,SAAA,GAA8C;IAA9C9B,EAAA,CAAAmC,UAAA,UAAAqC,QAAA,CAAAJ,UAAA,IAAAI,QAAA,CAAAJ,UAAA,MAA8C;IAWhDpE,EAAA,CAAA8B,SAAA,GAAyF;IAAzF9B,EAAA,CAAAkF,WAAA,gBAAAV,QAAA,CAAAH,KAAA,IAAAG,QAAA,CAAAH,KAAA,CAAAe,IAAA,aAAAZ,QAAA,CAAAH,KAAA,CAAAe,IAAA,gBAAyF;IAF/BpF,EAAA,CAAAiD,gBAAA,YAAAuB,QAAA,CAAAH,KAAA,CAAwB;IAKhFrE,EAAA,CAAA8B,SAAA,GAA0E;IAA1E9B,EAAA,CAAAmC,UAAA,UAAAqC,QAAA,CAAAH,KAAA,IAAAG,QAAA,CAAAH,KAAA,CAAAe,IAAA,aAAAZ,QAAA,CAAAH,KAAA,CAAAe,IAAA,gBAA0E;IAiB5EpF,EAAA,CAAA8B,SAAA,GAAuB;IAAvB9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAA6E,WAAA,CAAAX,QAAA,EAAuB;IAGvBxE,EAAA,CAAA8B,SAAA,EAAwB;IAAxB9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAA6E,WAAA,CAAAX,QAAA,EAAwB;;;;;IAnElCxE,EAJJ,CAAAC,cAAA,eAEgC,eACE,aACiB;IAC7CD,EAAA,CAAAY,SAAA,aAA+B;IAAAZ,EAAA,CAAAa,MAAA,wDAC/B;IAAAb,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAa,MAAA,GACI;IAC1Cb,EAD0C,CAAAc,YAAA,EAAO,EAC5C;IACLd,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAa,MAAA,uGAAgB;IAC5Cb,EAD4C,CAAAc,YAAA,EAAQ,EAC9C;IACNd,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAA2B,UAAA,KAAA0D,uDAAA,qBAEuC;IA8DzCrF,EAAA,CAAAc,YAAA,EAAM;IAOEd,EAJR,CAAAC,cAAA,gBAA6D,eAC1C,cACO,gBACM,gBACC;IAAAD,EAAA,CAAAa,MAAA,4CAAM;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACtCd,EAAA,CAAAC,cAAA,iBAA4C;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IAEvFb,EAFuF,CAAAc,YAAA,EAAO,EACtF,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACM,gBACC;IAAAD,EAAA,CAAAa,MAAA,4CAAM;IAAAb,EAAA,CAAAc,YAAA,EAAO;IACtCd,EAAA,CAAAC,cAAA,iBAEgF;IAC9ED,EAAA,CAAAa,MAAA,IACF;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACH,EACF,EACF;IACNd,EAAA,CAAAC,cAAA,gBAAgD;IAC9CD,EAAA,CAAAY,SAAA,gBAEM;IAGZZ,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;;;;IAlGoCd,EAAA,CAAA8B,SAAA,GACI;IADJ9B,EAAA,CAAAsF,kBAAA,KAAAhF,MAAA,CAAAiF,kBAAA,SAAAjF,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,wBACI;IAKpBzD,EAAA,CAAA8B,SAAA,GAA6B;IAA7B9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAoD,wBAAA,CAA6B;IAwED1D,EAAA,CAAA8B,SAAA,GAAuC;IAAvC9B,EAAA,CAAA+B,kBAAA,KAAAzB,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,YAAuC;IAOjFzD,EAAA,CAAA8B,SAAA,GAA+E;IAC/E9B,EADA,CAAAkF,WAAA,iBAAA5E,MAAA,CAAAiF,kBAAA,OAAAjF,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,CAA+E,iBAAAnD,MAAA,CAAAiF,kBAAA,KAAAjF,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,CACF;IAC7EzD,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAAzB,MAAA,CAAAiF,kBAAA,eACF;IAMFvF,EAAA,CAAA8B,SAAA,GAA0H;IAA1H9B,EAAA,CAAAwF,WAAA,UAAAlF,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,OAAAnD,MAAA,CAAAiF,kBAAA,KAAAjF,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,gBAA0H;;;;;;IAjU1IzD,EAHJ,CAAAC,cAAA,kBAC0F,yBACU,aACjD;IAC7CD,EAAA,CAAAY,SAAA,YAC2E;IAC3EZ,EAAA,CAAAa,MAAA,GACF;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAuF,kEAAA;MAAA,MAAAC,OAAA,GAAA1F,EAAA,CAAAI,aAAA,CAAAuF,IAAA,EAAAC,SAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAuF,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAE5F1F,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAUHd,EARd,CAAAC,cAAA,uBAAgC,cACb,cAEK,eACW,eACL,cACE,eACwB,iBAEqB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAEzEd,EADF,CAAAC,cAAA,eAA8B,iBAI6C;IAFvED,EAAA,CAAA4C,gBAAA,2BAAAkD,0EAAAhD,MAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAgD,kBAAA,CAAA1C,MAAA,CAAAoC,cAAA,CAAAR,aAAA,EAAAY,MAAA,MAAAxC,MAAA,CAAAoC,cAAA,CAAAR,aAAA,GAAAY,MAAA;MAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;IAAA,EAA0C;IAC1C9C,EAAA,CAAAE,UAAA,mCAAA6F,kFAAA;MAAA,MAAAL,OAAA,GAAA1F,EAAA,CAAAI,aAAA,CAAAuF,IAAA,EAAAC,SAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAAyBJ,MAAA,CAAA0F,QAAA,CAAAN,OAAA,CAAa;IAAA,EAAC;IAI/C1F,EANM,CAAAc,YAAA,EAGyE,EACrE,EACF,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,eACwB,iBAEqB;IAAAD,EAAA,CAAAa,MAAA,oBAAE;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAGrEd,EAFJ,CAAAC,cAAA,eAA8B,yBACE,qBAEe;IADZD,EAAA,CAAA4C,gBAAA,2BAAAqD,8EAAAnD,MAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAAuF,IAAA;MAAA,MAAArF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAgD,kBAAA,CAAA1C,MAAA,CAAAoC,cAAA,CAAAN,OAAA,EAAAU,MAAA,MAAAxC,MAAA,CAAAoC,cAAA,CAAAN,OAAA,GAAAU,MAAA;MAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;IAAA,EAAoC;IAG/D9C,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,qBACvD;IACFb,EADE,CAAAc,YAAA,EAAO,EACG;IAEVd,EADF,CAAAC,cAAA,qBAAuB,eACmB;IACtCD,EAAA,CAAAY,SAAA,aAAoD;IAAAZ,EAAA,CAAAa,MAAA,qBACtD;IAOdb,EAPc,CAAAc,YAAA,EAAO,EACG,EACF,EACE,EACZ,EACF,EACF,EACF;IAMAd,EAHN,CAAAC,cAAA,eAAsB,cACE,cACmB,iBAE0B;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC3Ed,EAAA,CAAAC,cAAA,eAA8B;IAS5BD,EAPA,CAAA2B,UAAA,KAAAuE,gDAAA,kBACuE,KAAAC,0DAAA,6BAMrB;IAiBxDnG,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;IACNd,EAAA,CAAAY,SAAA,cAEM;IAIdZ,EAHM,CAAAc,YAAA,EAAM,EACF,EACF,EACF;IAOEd,EAJR,CAAAC,cAAA,eAAoB,eACW,eACV,eACiC,iBAEiB;IAAAD,EAAA,CAAAa,MAAA,gCAAI;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAC3Ed,EAAA,CAAAC,cAAA,eAA8B;IAqD5BD,EAnDA,CAAA2B,UAAA,KAAAyE,gDAAA,kBAAqC,KAAAC,gDAAA,kBAmDC;IAO1CrG,EADE,CAAAc,YAAA,EAAM,EACF;IACNd,EAAA,CAAAC,cAAA,eAAuB;IA0DrBD,EAxDA,CAAA2B,UAAA,KAAA2E,gDAAA,mBAEkC,KAAAC,gDAAA,oBAwDF;IA2G1CvG,EAJQ,CAAAc,YAAA,EAAM,EACF,EACF,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,kBAEjD;IADTD,EAAA,CAAAE,UAAA,mBAAAsG,mEAAA;MAAA,MAAAd,OAAA,GAAA1F,EAAA,CAAAI,aAAA,CAAAuF,IAAA,EAAAC,SAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAuF,OAAA,CAAAH,OAAA,CAAY;IAAA,EAAC;IAExE1F,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAAAb,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAC4H;IADvFD,EAAA,CAAAE,UAAA,mBAAAuG,mEAAA;MAAA,MAAAf,OAAA,GAAA1F,EAAA,CAAAI,aAAA,CAAAuF,IAAA,EAAAC,SAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAA0F,QAAA,CAAAN,OAAA,CAAa;IAAA,EAAC;IAE1D1F,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,IACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IAzVDd,EAAA,CAAAwF,WAAA,UAAAlF,MAAA,CAAAoG,UAAA,CAA0B;IAIT1G,EAAA,CAAA8B,SAAA,GAAoC;IACnB9B,EADjB,CAAAkF,WAAA,oBAAA5E,MAAA,CAAAqG,UAAA,CAAoC,YAAArG,MAAA,CAAAqG,UAAA,CAA6B,kBAAArG,MAAA,CAAAqG,UAAA,CACjD,iBAAArG,MAAA,CAAAqG,UAAA,CAAkC;IACtE3G,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,MAAAzB,MAAA,CAAAqG,UAAA,gEACF;IAmBgB3G,EAAA,CAAA8B,SAAA,IAA0C;IAA1C9B,EAAA,CAAAiD,gBAAA,YAAA3C,MAAA,CAAAoC,cAAA,CAAAR,aAAA,CAA0C;IAYXlC,EAAA,CAAA8B,SAAA,GAAoC;IAApC9B,EAAA,CAAAiD,gBAAA,YAAA3C,MAAA,CAAAoC,cAAA,CAAAN,OAAA,CAAoC;IAEtDpC,EAAA,CAAA8B,SAAA,EAAW;IAAX9B,EAAA,CAAAmC,UAAA,YAAW;IAKXnC,EAAA,CAAA8B,SAAA,GAAW;IAAX9B,EAAA,CAAAmC,UAAA,YAAW;IAoBpBnC,EAAA,CAAA8B,SAAA,IAAgB;IAAhB9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAqG,UAAA,CAAgB;IAON3G,EAAA,CAAA8B,SAAA,EAAiB;IAAjB9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAqG,UAAA,CAAiB;IAmC/B3G,EAAA,CAAA8B,SAAA,GAAgB;IAAhB9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAqG,UAAA,CAAgB;IAmDhB3G,EAAA,CAAA8B,SAAA,EAAiB;IAAjB9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAqG,UAAA,CAAiB;IAWtB3G,EAAA,CAAA8B,SAAA,GAAyH;IAAzH9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAqG,UAAA,IAAArG,MAAA,CAAAoC,cAAA,CAAAV,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAAoD,YAAA,IAAA5C,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,KAAyH;IAwDzHzD,EAAA,CAAA8B,SAAA,EAA0H;IAA1H9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAqG,UAAA,IAAArG,MAAA,CAAAoC,cAAA,CAAAV,aAAA,KAAA1B,MAAA,CAAAR,gBAAA,CAAAoD,YAAA,IAAA5C,MAAA,CAAAoD,wBAAA,CAAAD,MAAA,KAA0H;IAqHlGzD,EAAA,CAAA8B,SAAA,GACnC;IADmC9B,EAAA,CAAA+B,kBAAA,KAAAzB,MAAA,CAAAqG,UAAA,gEACnC;;;;;IAoGI3G,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAY,SAAA,aAAmF;IACnFZ,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAa,MAAA,4BAAM;IACjCb,EADiC,CAAAc,YAAA,EAAO,EAClC;;;;;IAeId,EADF,CAAAC,cAAA,SAA8D,SACxD;IAAAD,EAAA,CAAAa,MAAA,GAAW;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpBd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAA2C;IAAAZ,EAAA,CAAAa,MAAA,GAC7C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAY,SAAA,aAAqD;IAAAZ,EAAA,CAAAa,MAAA,GACvD;IACFb,EADE,CAAAc,YAAA,EAAK,EACF;;;;;IAPCd,EAAA,CAAA8B,SAAA,GAAW;IAAX9B,EAAA,CAAAiC,iBAAA,CAAA2E,KAAA,KAAW;IAE8B5G,EAAA,CAAA8B,SAAA,GAC7C;IAD6C9B,EAAA,CAAA+B,kBAAA,KAAA8E,SAAA,CAAAzD,KAAA,MAC7C;IAEuDpD,EAAA,CAAA8B,SAAA,GACvD;IADuD9B,EAAA,CAAA+B,kBAAA,KAAA8E,SAAA,CAAAxD,SAAA,aACvD;;;;;IAbArD,EAJR,CAAAC,cAAA,eAAsE,iBACtC,YACrB,SACD,cAC4B;IAAAD,EAAA,CAAAa,MAAA,QAAC;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACpCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACvCd,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAa,MAAA,+BAAI;IAEtCb,EAFsC,CAAAc,YAAA,EAAK,EACpC,EACC;IACRd,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA2B,UAAA,KAAAmF,4DAAA,iBAA8D;IAWpE9G,EAFI,CAAAc,YAAA,EAAQ,EACF,EACJ;;;;IAXsBd,EAAA,CAAA8B,SAAA,IAAyB;IAAzB9B,EAAA,CAAAmC,UAAA,YAAA7B,MAAA,CAAAyG,oBAAA,CAAyB;;;;;IAcrD/G,EAAA,CAAAC,cAAA,eAAmF;IACjFD,EAAA,CAAAY,SAAA,YAAuC;IAAAZ,EAAA,CAAAa,MAAA,0EACzC;IAAAb,EAAA,CAAAc,YAAA,EAAM;;;;;IA3BRd,EAAA,CAAAC,cAAA,UAAsC;IAyBpCD,EAxBA,CAAA2B,UAAA,IAAAqF,sDAAA,oBAAsE,IAAAC,sDAAA,mBAwBa;IAGrFjH,EAAA,CAAAc,YAAA,EAAM;;;;IA3B2Bd,EAAA,CAAA8B,SAAA,EAAqC;IAArC9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAyG,oBAAA,CAAAtD,MAAA,KAAqC;IAwB9DzD,EAAA,CAAA8B,SAAA,EAAuC;IAAvC9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAyG,oBAAA,CAAAtD,MAAA,OAAuC;;;;;;IAxHnDzD,EAFJ,CAAAC,cAAA,mBAA+G,yBACX,aACjD;IAC7CD,EAAA,CAAAY,SAAA,aAAyC;IAAAZ,EAAA,CAAAa,MAAA,gCAC3C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBACkG;IAD1BD,EAAA,CAAAE,UAAA,mBAAAgH,kEAAA;MAAA,MAAAC,OAAA,GAAAnH,EAAA,CAAAI,aAAA,CAAAgH,IAAA,EAAAxB,SAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAuF,OAAA,CAAAsB,OAAA,CAAY;IAAA,EAAC;IAE5FnH,EAAA,CAAAY,SAAA,YAA4B;IAEhCZ,EADE,CAAAc,YAAA,EAAS,EACM;IAMXd,EAJN,CAAAC,cAAA,uBAAgC,eAEgD,eACkB,eAChD;IAC1CD,EAAA,CAAAY,SAAA,cAAoD;IAAAZ,EAAA,CAAAa,MAAA,iCACtD;IACFb,EADE,CAAAc,YAAA,EAAK,EACD;IAKEd,EAJR,CAAAC,cAAA,gBAAuB,eACJ,cACO,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAA4C;IAAAZ,EAAA,CAAAa,MAAA,iCAC9C;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAAkD;IAEtEb,EAFsE,CAAAc,YAAA,EAAI,EAClE,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,qBACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IAENd,EADF,CAAAC,cAAA,cAAgB,gBAE4E;IACxFD,EAAA,CAAAY,SAAA,cACmB;IACnBZ,EAAA,CAAAa,MAAA,IACF;IAGNb,EAHM,CAAAc,YAAA,EAAO,EACL,EACA,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAEjEb,EAFiE,CAAAc,YAAA,EAAI,EAC7D,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAmD;IAAAZ,EAAA,CAAAa,MAAA,iCACrD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA2E;;IAE/Fb,EAF+F,CAAAc,YAAA,EAAI,EAC3F,EACF;IAGFd,EAFJ,CAAAC,cAAA,cAAsB,gBACS,kBACgB;IACzCD,EAAA,CAAAY,SAAA,cAAkD;IAAAZ,EAAA,CAAAa,MAAA,2BACpD;IAAAb,EAAA,CAAAc,YAAA,EAAQ;IACRd,EAAA,CAAAC,cAAA,cAAgB;IAAAD,EAAA,CAAAa,MAAA,IAA6C;IAKvEb,EALuE,CAAAc,YAAA,EAAI,EAC7D,EACF,EACF,EACF,EACF;IAMFd,EAHJ,CAAAC,cAAA,gBAAyE,gBAEA,eACzB;IAC1CD,EAAA,CAAAY,SAAA,cAA6C;IAAAZ,EAAA,CAAAa,MAAA,uCAC/C;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACLd,EAAA,CAAAC,cAAA,iBAA+B;IAAAD,EAAA,CAAAa,MAAA,IAAuC;IACxEb,EADwE,CAAAc,YAAA,EAAO,EACzE;IACNd,EAAA,CAAAC,cAAA,gBAAuB;IAQrBD,EANA,CAAA2B,UAAA,KAAA0F,gDAAA,kBAA8D,KAAAC,gDAAA,kBAMxB;IA+B5CtH,EAFI,CAAAc,YAAA,EAAM,EACF,EACO;IAGbd,EADF,CAAAC,cAAA,0BAAgH,mBAEjD;IADtBD,EAAA,CAAAE,UAAA,mBAAAqH,mEAAA;MAAA,MAAAJ,OAAA,GAAAnH,EAAA,CAAAI,aAAA,CAAAgH,IAAA,EAAAxB,SAAA;MAAA,MAAAtF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAU,WAAA,CAASJ,MAAA,CAAAuF,OAAA,CAAAsB,OAAA,CAAY;IAAA,EAAC;IAE3DnH,EAAA,CAAAY,SAAA,aAAiC;IAAAZ,EAAA,CAAAa,MAAA,qBACnC;IAEJb,EAFI,CAAAc,YAAA,EAAS,EACM,EACT;;;;IA9GoBd,EAAA,CAAA8B,SAAA,IAAkD;IAAlD9B,EAAA,CAAAiC,iBAAA,EAAA3B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAtF,aAAA,SAAkD;IAU9DlC,EAAA,CAAA8B,SAAA,GAAuF;IAAvF9B,EAAA,CAAAmC,UAAA,aAAA7B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAApF,OAAA,8CAAuF;IACpFpC,EAAA,CAAA8B,SAAA,EAA+F;IAA/F9B,EAAA,CAAAyH,UAAA,EAAAnH,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAApF,OAAA,wDAA+F;IAElGpC,EAAA,CAAA8B,SAAA,EACF;IADE9B,EAAA,CAAA+B,kBAAA,OAAAzB,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAApF,OAAA,+CACF;IAScpC,EAAA,CAAA8B,SAAA,GAA2E;IAA3E9B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAqC,WAAA,SAAA/B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAlF,SAAA,6BAA2E;IAQ3EtC,EAAA,CAAA8B,SAAA,GAA6C;IAA7C9B,EAAA,CAAAiC,iBAAA,EAAA3B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAjF,QAAA,SAA6C;IAQ7CvC,EAAA,CAAA8B,SAAA,GAA2E;IAA3E9B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAqC,WAAA,SAAA/B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAE,SAAA,6BAA2E;IAQ3E1H,EAAA,CAAA8B,SAAA,GAA6C;IAA7C9B,EAAA,CAAAiC,iBAAA,EAAA3B,MAAA,CAAAkH,sBAAA,kBAAAlH,MAAA,CAAAkH,sBAAA,CAAAG,QAAA,SAA6C;IAcpC3H,EAAA,CAAA8B,SAAA,GAAuC;IAAvC9B,EAAA,CAAA+B,kBAAA,YAAAzB,MAAA,CAAAyG,oBAAA,CAAAtD,MAAA,wBAAuC;IAIhEzD,EAAA,CAAA8B,SAAA,GAA6B;IAA7B9B,EAAA,CAAAmC,UAAA,SAAA7B,MAAA,CAAAsH,uBAAA,CAA6B;IAM7B5H,EAAA,CAAA8B,SAAA,EAA8B;IAA9B9B,EAAA,CAAAmC,UAAA,UAAA7B,MAAA,CAAAsH,uBAAA,CAA8B;;;AD5gB9C,OAAM,MAAOC,iBAAkB,SAAQpI,aAAa;EASlDqI,YACqBC,KAAkB,EAC7BC,aAA8B,EAC9BC,gBAAiC,EACjCC,aAA2B,EAC3BC,OAAuB,EACvBC,KAAuB;IAE/B,KAAK,CAACL,KAAK,CAAC;IAPO,KAAAA,KAAK,GAALA,KAAK;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,KAAK,GAALA,KAAK;IAdf,KAAAC,IAAI,GAAGA,IAAI,CAAC,CAAC;IACb,KAAAvI,gBAAgB,GAAGA,gBAAgB,CAAC,CAAC;IACrC,KAAAC,sBAAsB,GAAGA,sBAAsB,CAAC,CAAC;IAiBxC,KAAAuI,SAAS,GAAG,CAAC;IACb,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,SAAS,GAAG,CAAC;IACb,KAAAC,YAAY,GAAG,CAAC;IAEzB;IACA,KAAAC,YAAY,GAAmB,EAAE;IACjC,KAAAhG,cAAc,GAAqB,EAAE;IACrC,KAAAiG,aAAa,GAAW,EAAE;IAC1B,KAAAC,YAAY,GAAkB,IAAI;IAClC,KAAAC,kBAAkB,GAAkB,IAAI;IAExC;IACA,KAAAC,eAAe,GAAwB,EAAE;IACzC,KAAAvF,yBAAyB,GAAwB,EAAE;IACnD,KAAAwF,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,mBAAmB,GAAW,EAAE;IAChC,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,aAAa,GAAG,EAAE;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,iBAAiB,GAAG,KAAK;IAEzB;IACA,KAAAC,yBAAyB,GAAuB,EAAE;IAClD,KAAA3F,wBAAwB,GAAuB,EAAE;IACjD,KAAA4F,iBAAiB,GAAW,EAAE;IAC9B,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,aAAa,GAAG,CAAC;IACjB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IACpB,KAAAC,gBAAgB,GAAG,KAAK;IAExB;IACA,KAAAnC,sBAAsB,GAAwB,IAAI;IAClD,KAAAT,oBAAoB,GAA8B,EAAE;IACpD,KAAAa,uBAAuB,GAAG,KAAK;IAE/B;IACA,KAAAgC,uBAAuB,GAAyB,EAAE;IAClD,KAAA7F,wBAAwB,GAAG,KAAK;IAEhC;IACA,KAAA4C,UAAU,GAAG,KAAK;EA5ClB;EA8CSkD,QAAQA,CAAA;IACf,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEA;EACAD,gBAAgBA,CAAA;IACd,MAAME,OAAO,GAAG;MACd9H,aAAa,EAAE,IAAI,CAACyG,aAAa,IAAI,IAAI;MACzCvG,OAAO,EAAE,IAAI,CAACwG,YAAY;MAC1B5G,aAAa,EAAE,IAAI,CAAC6G,kBAAkB;MACtCoB,SAAS,EAAE,IAAI,CAACzB,SAAS;MACzB0B,QAAQ,EAAE,IAAI,CAAC3B;KAChB;IAED,IAAI,CAACN,gBAAgB,CAACkC,mCAAmC,CAAC;MAAEC,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CAC/ExK,GAAG,CAACyK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAC7B,YAAY,GAAG4B,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACjDC,WAAW,EAAED,IAAI,CAACC,WAAY;UAC9BzI,aAAa,EAAEwI,IAAI,CAACxI,aAAc;UAClCF,aAAa,EAAE0I,IAAI,CAAC1I,aAAa;UAAE;UACnCM,SAAS,EAAEoI,IAAI,CAACpI,SAAU;UAC1BoF,SAAS,EAAEgD,IAAI,CAAChD,SAAU;UAC1BnF,QAAQ,EAAEmI,IAAI,CAACnI,QAAQ;UACvBoF,QAAQ,EAAE+C,IAAI,CAAC/C,QAAQ;UACvBvF,OAAO,EAAEsI,IAAI,CAACtI;SACf,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACqG,YAAY,GAAG6B,QAAQ,CAACM,UAAU,IAAI,CAAC;MAC9C,CAAC,MAAM;QACL,IAAI,CAACzC,OAAO,CAAC0C,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACAhB,mBAAmBA,CAAA;IACjB,MAAMC,OAAO,GAAG;MACd5G,KAAK,EAAE,IAAI,CAAC2F,kBAAkB,IAAI,IAAI;MACtC1F,SAAS,EAAE,IAAI,CAAC2F,mBAAmB,IAAI,IAAI;MAC3C5G,OAAO,EAAE,CAAC;MAAE;MACZ6H,SAAS,EAAE,IAAI,CAAChB,cAAc;MAC9BiB,QAAQ,EAAE,IAAI,CAAChB;KAChB;IAED,IAAI,CAAChB,aAAa,CAAC8C,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtExK,GAAG,CAACyK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACzB,eAAe,GAAGwB,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACpDO,QAAQ,EAAEP,IAAI,CAACO,QAAS;UACxB7H,KAAK,EAAEsH,IAAI,CAACtH,KAAM;UAClBC,SAAS,EAAEqH,IAAI,CAACrH,SAAS;UACzB6H,QAAQ,EAAE,IAAI,CAAC3H,yBAAyB,CAAC4H,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ;SAChF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAAC9B,iBAAiB,GAAGmB,QAAQ,CAACM,UAAU,IAAI,CAAC;QACjD,IAAI,CAACS,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,CACH,CAACN,SAAS,EAAE;EACf;EAEA;EACAO,QAAQA,CAAA;IACN,IAAI,CAAC9C,SAAS,GAAG,CAAC;IAClB,IAAI,CAACsB,gBAAgB,EAAE;EACzB;EAEAyB,OAAOA,CAAA;IACL,IAAI,CAAC5C,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACJ,SAAS,GAAG,CAAC;IAClB,IAAI,CAACsB,gBAAgB,EAAE;EACzB;EAEA;EACA0B,6BAA6BA,CAAA;IAC3B,MAAMxB,OAAO,GAAG;MACd5G,KAAK,EAAE,IAAI,CAACkG,iBAAiB,IAAI,IAAI;MACrCjG,SAAS,EAAE,IAAI,CAACkG,kBAAkB,IAAI,IAAI;MAC1CnH,OAAO,EAAE,CAAC;MAAE;MACZ6H,SAAS,EAAE,IAAI,CAACT,aAAa;MAC7BU,QAAQ,EAAE,IAAI,CAACT;KAChB;IAED,IAAI,CAACvB,aAAa,CAAC8C,6BAA6B,CAAC;MAAEZ,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACtExK,GAAG,CAACyK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAAClB,yBAAyB,GAAGiB,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UAC9DO,QAAQ,EAAEP,IAAI,CAACO,QAAS;UACxB7H,KAAK,EAAEsH,IAAI,CAACtH,KAAM;UAClBC,SAAS,EAAEqH,IAAI,CAACrH,SAAS;UACzB6H,QAAQ,EAAE,IAAI,CAACxH,wBAAwB,CAACyH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;UAC/E7G,UAAU,EAAE,IAAI,CAACV,wBAAwB,CAAC+H,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE7G,UAAU,IAAI,CAAC;UAClGC,KAAK,EAAE,IAAI,CAACX,wBAAwB,CAAC+H,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE5G,KAAK,IAAI;SACxF,CAAC,CAAC,IAAI,EAAE;QACT,IAAI,CAACqF,gBAAgB,GAAGY,QAAQ,CAACM,UAAU,IAAI,CAAC;QAChD,IAAI,CAACc,2BAA2B,EAAE;MACpC;IACF,CAAC,CAAC,CACH,CAACX,SAAS,EAAE;EACf;EAEA;EACAY,aAAaA,CAAA;IACX,IAAI,CAAC1C,cAAc,GAAG,CAAC;IACvB,IAAI,CAACc,mBAAmB,EAAE;EAC5B;EAEA6B,YAAYA,CAAA;IACV,IAAI,CAAC7C,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAACC,cAAc,GAAG,CAAC;IACvB,IAAI,CAACc,mBAAmB,EAAE;EAC5B;EAEA;EACA8B,YAAYA,CAAA;IACV,IAAI,CAACrC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACgC,6BAA6B,EAAE;EACtC;EAEAM,WAAWA,CAAA;IACT,IAAI,CAACxC,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACgC,6BAA6B,EAAE;EACtC;EAEA;EACAO,WAAWA,CAACC,IAAY;IACtB,IAAI,CAACxD,SAAS,GAAGwD,IAAI;IACrB,IAAI,CAAClC,gBAAgB,EAAE;EACzB;EAEAmC,gBAAgBA,CAACD,IAAY;IAC3B,IAAI,CAAC/C,cAAc,GAAG+C,IAAI;IAC1B,IAAI,CAACjC,mBAAmB,EAAE;EAC5B;EAEAmC,eAAeA,CAACF,IAAY;IAC1B,IAAI,CAACxC,aAAa,GAAGwC,IAAI;IACzB,IAAI,CAACR,6BAA6B,EAAE;EACtC;EAEA;EACA7K,eAAeA,CAACwL,KAAuB;IACrC,IAAI,CAACxF,UAAU,GAAG,KAAK;IACvB,IAAI,CAACjE,cAAc,GAAG;MACpBN,OAAO,EAAE,CAAC;MACVJ,aAAa,EAAElC,gBAAgB,CAAC6C,aAAa,CAAC;KAC/C;IACD,IAAI,CAACY,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACG,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAACqG,mBAAmB,EAAE;IAC1B,IAAI,CAACyB,6BAA6B,EAAE;IACpC,IAAI,CAACxD,aAAa,CAACoE,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAnL,aAAaA,CAACgL,KAAuB,EAAEI,QAAsB;IAC3D,IAAI,CAAC5F,UAAU,GAAG,IAAI;IACtB,IAAI,CAACjE,cAAc,GAAG;MACpBiI,WAAW,EAAE4B,QAAQ,CAAC5B,WAAW;MACjCzI,aAAa,EAAEqK,QAAQ,CAACrK,aAAa;MACrCF,aAAa,EAAEuK,QAAQ,CAACvK,aAAa,IAAIlC,gBAAgB,CAAC6C,aAAa;MACvEP,OAAO,EAAEmK,QAAQ,CAACnK,OAAO,IAAI;KAC9B;IAED;IACA,IAAI,CAACmB,yBAAyB,GAAG,EAAE;IACnC,IAAI,CAACG,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAACkG,uBAAuB,GAAG,EAAE;IAEjC;IACA,IAAI,CAAC4C,2BAA2B,CAACD,QAAQ,CAAC5B,WAAW,CAAC;IAEtD,IAAI,CAAC3C,aAAa,CAACoE,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAzG,OAAOA,CAAC4G,GAAQ;IACdA,GAAG,CAACC,KAAK,EAAE;EACb;EAEA;EACA,IAAIhG,UAAUA,CAAA;IACZ,OAAO,OAAO;EAChB;EAEAV,QAAQA,CAACyG,GAAQ;IACf,IAAI,CAAC,IAAI,CAACE,oBAAoB,EAAE,EAAE;MAChC;IACF;IAEA,IAAI,IAAI,CAACjK,cAAc,CAACiI,WAAW,EAAE;MACnC,IAAI,CAACiC,cAAc,CAACH,GAAG,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACI,cAAc,CAACJ,GAAG,CAAC;IAC1B;EACF;EAEA;EACAE,oBAAoBA,CAAA;IAClB,IAAI,CAAC,IAAI,CAACjK,cAAc,CAACR,aAAa,EAAEkD,IAAI,EAAE,EAAE;MAC9C,IAAI,CAAC+C,OAAO,CAAC0C,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACnI,cAAc,CAACV,aAAa,KAAK8K,SAAS,IAAI,IAAI,CAACpK,cAAc,CAACV,aAAa,KAAK,IAAI,EAAE;MACjG,IAAI,CAACmG,OAAO,CAAC0C,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACnI,cAAc,CAACN,OAAO,KAAK0K,SAAS,IAAI,IAAI,CAACpK,cAAc,CAACN,OAAO,KAAK,IAAI,EAAE;MACrF,IAAI,CAAC+F,OAAO,CAAC0C,YAAY,CAAC,SAAS,CAAC;MACpC,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAACnI,cAAc,CAACiI,WAAW,IAAI,IAAI,CAACjI,cAAc,CAACV,aAAa,KAAKlC,gBAAgB,CAAC6C,aAAa,IAAI,IAAI,CAACY,yBAAyB,CAACE,MAAM,KAAK,CAAC,EAAE;MAC3J,IAAI,CAAC0E,OAAO,CAAC0C,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA,IAAI,CAAC,IAAI,CAACnI,cAAc,CAACiI,WAAW,IAAI,IAAI,CAACjI,cAAc,CAACV,aAAa,KAAKlC,gBAAgB,CAACoD,YAAY,IAAI,IAAI,CAACQ,wBAAwB,CAACD,MAAM,KAAK,CAAC,EAAE;MACzJ,IAAI,CAAC0E,OAAO,CAAC0C,YAAY,CAAC,eAAe,CAAC;MAC1C,OAAO,KAAK;IACd;IAEA;IACA,IAAI,IAAI,CAACnI,cAAc,CAACV,aAAa,KAAKlC,gBAAgB,CAACoD,YAAY,EAAE;MACvE,KAAK,MAAMwH,IAAI,IAAI,IAAI,CAAChH,wBAAwB,EAAE;QAChD;QACA,IAAIgH,IAAI,CAACtG,UAAU,KAAK0I,SAAS,IAAIpC,IAAI,CAACtG,UAAU,KAAK,IAAI,IAAIsG,IAAI,CAACtG,UAAU,IAAI,CAAC,EAAE;UACrF,IAAI,CAAC+D,OAAO,CAAC0C,YAAY,CAAC,MAAMH,IAAI,CAACtH,KAAK,iBAAiB,CAAC;UAC5D,OAAO,KAAK;QACd;QAEA;QACA,IAAI2J,KAAK,CAACrC,IAAI,CAACtG,UAAU,CAAC,EAAE;UAC1B,IAAI,CAAC+D,OAAO,CAAC0C,YAAY,CAAC,MAAMH,IAAI,CAACtH,KAAK,aAAa,CAAC;UACxD,OAAO,KAAK;QACd;QAEA;QACA,IAAI,CAACsH,IAAI,CAACrG,KAAK,IAAIqG,IAAI,CAACrG,KAAK,CAACe,IAAI,EAAE,KAAK,EAAE,IAAIsF,IAAI,CAACrG,KAAK,CAACe,IAAI,EAAE,KAAK,GAAG,EAAE;UACxE,IAAI,CAAC+C,OAAO,CAAC0C,YAAY,CAAC,MAAMH,IAAI,CAACtH,KAAK,wBAAwB,CAAC;UACnE,OAAO,KAAK;QACd;QAEA;QACA,IAAIsH,IAAI,CAACrG,KAAK,CAACe,IAAI,EAAE,CAAC3B,MAAM,GAAG,EAAE,EAAE;UACjC,IAAI,CAAC0E,OAAO,CAAC0C,YAAY,CAAC,MAAMH,IAAI,CAACtH,KAAK,iBAAiB,CAAC;UAC5D,OAAO,KAAK;QACd;MACF;IACF;IAEA,OAAO,IAAI;EACb;EAEA;EACAyJ,cAAcA,CAACJ,GAAQ;IACrB,IAAIO,OAAO,GAAU,EAAE;IAEvB,IAAI,IAAI,CAACtK,cAAc,CAACV,aAAa,KAAKlC,gBAAgB,CAAC6C,aAAa,EAAE;MACxE;MACAqK,OAAO,GAAG,IAAI,CAACzJ,yBAAyB,CAACkH,GAAG,CAACwC,KAAK,KAAK;QACrDC,iBAAiB,EAAE,IAAI;QACvBC,UAAU,EAAEF,KAAK,CAAChC,QAAQ;QAC1B7H,KAAK,EAAE6J,KAAK,CAAC7J,KAAK;QAClBC,SAAS,EAAE4J,KAAK,CAAC5J;OAClB,CAAC,CAAC;IACL,CAAC,MAAM,IAAI,IAAI,CAACX,cAAc,CAACV,aAAa,KAAKlC,gBAAgB,CAACoD,YAAY,EAAE;MAC9E;MACA8J,OAAO,GAAG,IAAI,CAACtJ,wBAAwB,CAAC+G,GAAG,CAACC,IAAI,KAAK;QACnDwC,iBAAiB,EAAE,IAAI;QACvBC,UAAU,EAAEzC,IAAI,CAACO,QAAQ;QACzB7H,KAAK,EAAEsH,IAAI,CAACtH,KAAK;QACjBC,SAAS,EAAEqH,IAAI,CAACrH,SAAS;QACzBe,UAAU,EAAEsG,IAAI,CAACtG,UAAU;QAC3BC,KAAK,EAAEqG,IAAI,CAACrG;OACb,CAAC,CAAC;IACL;IAEA,MAAM+I,YAAY,GAAqB;MACrClL,aAAa,EAAE,IAAI,CAACQ,cAAc,CAACR,aAAa;MAChDF,aAAa,EAAE,IAAI,CAACU,cAAc,CAACV,aAAa;MAChDI,OAAO,EAAE,IAAI,CAACM,cAAc,CAACN,OAAO;MACpCiL,OAAO,EAAEL,OAAO,CAACvJ,MAAM,GAAG,CAAC,GAAGuJ,OAAO,GAAGF;KACzC;IAED,IAAI,CAAC7E,gBAAgB,CAACqF,gCAAgC,CAAC;MAAElD,IAAI,EAAEgD;IAAY,CAAE,CAAC,CAAC/C,IAAI,CACjFxK,GAAG,CAACyK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACpC,OAAO,CAACoF,aAAa,CAAC,QAAQ,CAAC;QACpCd,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAAC5C,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAAC3B,OAAO,CAAC0C,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA6B,cAAcA,CAACH,GAAQ;IACrB,MAAMW,YAAY,GAAqB;MACrCzC,WAAW,EAAE,IAAI,CAACjI,cAAc,CAACiI,WAAW;MAC5CzI,aAAa,EAAE,IAAI,CAACQ,cAAc,CAACR,aAAa;MAChDF,aAAa,EAAE,IAAI,CAACU,cAAc,CAACV,aAAa;MAChDI,OAAO,EAAE,IAAI,CAACM,cAAc,CAACN;KAC9B;IAED,IAAI,CAAC6F,gBAAgB,CAACqF,gCAAgC,CAAC;MAAElD,IAAI,EAAEgD;IAAY,CAAE,CAAC,CAAC/C,IAAI,CACjFxK,GAAG,CAACyK,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACpC,OAAO,CAACoF,aAAa,CAAC,QAAQ,CAAC;QACpCd,GAAG,CAACC,KAAK,EAAE;QACX,IAAI,CAAC5C,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAAC3B,OAAO,CAAC0C,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;MACzD;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAGA;EACAzJ,cAAcA,CAACiL,QAAsB;IACnC,IAAIiB,OAAO,CAAC,WAAWjB,QAAQ,CAACrK,aAAa,KAAK,CAAC,EAAE;MACnD,IAAI,CAAC+F,gBAAgB,CAACwF,kCAAkC,CAAC;QACvDrD,IAAI,EAAE;UAAEO,WAAW,EAAE4B,QAAQ,CAAC5B;QAAW;OAC1C,CAAC,CAACN,IAAI,CACLxK,GAAG,CAACyK,QAAQ,IAAG;QACb,IAAIA,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;UAC7B,IAAI,CAACpC,OAAO,CAACoF,aAAa,CAAC,QAAQ,CAAC;UACpC,IAAI,CAACzD,gBAAgB,EAAE;QACzB,CAAC,MAAM;UACL,IAAI,CAAC3B,OAAO,CAAC0C,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,QAAQ,CAAC;QACzD;MACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;IACf;EACF;EAEA;EACAyB,2BAA2BA,CAACkB,UAAkB;IAC5C,IAAI,CAAC3J,wBAAwB,GAAG,IAAI;IAEpC,MAAMiG,OAAO,GAA8B;MACzC0D,UAAU,EAAEA;KACb;IAED,IAAI,CAACzF,gBAAgB,CAAC0F,yCAAyC,CAAC;MAAEvD,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACrFxK,GAAG,CAACyK,QAAQ,IAAG;MACb,IAAI,CAACvG,wBAAwB,GAAG,KAAK;MACrC,IAAIuG,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACX,uBAAuB,GAAGU,QAAQ,CAACE,OAAO,IAAI,EAAE;QAErD;QACA,IAAI,IAAI,CAAC9H,cAAc,CAACV,aAAa,KAAKlC,gBAAgB,CAAC6C,aAAa,EAAE;UACxE;UACA,IAAI,CAACY,yBAAyB,GAAG,IAAI,CAACqG,uBAAuB,CAACa,GAAG,CAACC,IAAI,KAAK;YACzEO,QAAQ,EAAEP,IAAI,CAACyC,UAAW;YAC1B/J,KAAK,EAAEsH,IAAI,CAACtH,KAAM;YAClBC,SAAS,EAAEqH,IAAI,CAACrH,SAAS;YACzB6H,QAAQ,EAAE;WACX,CAAC,CAAC;QACL,CAAC,MAAM;UACL;UACA,IAAI,CAACxH,wBAAwB,GAAG,IAAI,CAACkG,uBAAuB,CAACa,GAAG,CAACC,IAAI,KAAK;YACxEO,QAAQ,EAAEP,IAAI,CAACyC,UAAW;YAC1B/J,KAAK,EAAEsH,IAAI,CAACtH,KAAM;YAClBC,SAAS,EAAEqH,IAAI,CAACrH,SAAS;YACzBe,UAAU,EAAEsG,IAAI,CAACtG,UAAU,IAAI0I,SAAS;YACxCzI,KAAK,EAAEqG,IAAI,CAACrG,KAAK,IAAIyI,SAAS;YAC9B5B,QAAQ,EAAE;WACX,CAAC,CAAC;QACL;MACF,CAAC,MAAM;QACL,IAAI,CAAC/C,OAAO,CAAC0C,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACArJ,kBAAkBA,CAAC6K,QAAsB,EAAEJ,KAAuB;IAChE,IAAI,CAAC3E,sBAAsB,GAAG+E,QAAQ;IACtC,IAAI,CAAC3E,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACb,oBAAoB,GAAG,EAAE;IAE9B,IAAI,CAACiB,aAAa,CAACoE,IAAI,CAACD,KAAK,EAAE;MAC7BE,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE;KACZ,CAAC;IAEF,MAAMtC,OAAO,GAA8B;MACzC0D,UAAU,EAAEnB,QAAQ,CAAC5B;KACtB;IAED,IAAI,CAAC1C,gBAAgB,CAAC0F,yCAAyC,CAAC;MAAEvD,IAAI,EAAEJ;IAAO,CAAE,CAAC,CAACK,IAAI,CACrFxK,GAAG,CAACyK,QAAQ,IAAG;MACb,IAAI,CAAC1C,uBAAuB,GAAG,KAAK;MACpC,IAAI0C,QAAQ,CAACC,UAAU,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACxD,oBAAoB,GAAGuD,QAAQ,CAACE,OAAO,EAAEC,GAAG,CAACC,IAAI,KAAK;UACzDyC,UAAU,EAAEzC,IAAI,CAACyC,UAAW;UAC5B/J,KAAK,EAAEsH,IAAI,CAACtH,KAAM;UAClBC,SAAS,EAAEqH,IAAI,CAACrH;SACjB,CAAC,CAAC,IAAI,EAAE;MACX,CAAC,MAAM;QACL,IAAI,CAAC8E,OAAO,CAAC0C,YAAY,CAACP,QAAQ,CAACQ,OAAO,IAAI,UAAU,CAAC;MAC3D;IACF,CAAC,CAAC,CACH,CAACC,SAAS,EAAE;EACf;EAEA;EACA6C,oBAAoBA,CAACX,KAAwB;IAC3CA,KAAK,CAAC/B,QAAQ,GAAG,CAAC+B,KAAK,CAAC/B,QAAQ;IAEhC,IAAI+B,KAAK,CAAC/B,QAAQ,EAAE;MAClB,IAAI,CAAC,IAAI,CAAC3H,yBAAyB,CAAC4H,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgC,KAAK,CAAChC,QAAQ,CAAC,EAAE;QAC5E,IAAI,CAAC1H,yBAAyB,CAACsK,IAAI,CAAC;UAAE,GAAGZ;QAAK,CAAE,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAAC1J,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACuK,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgC,KAAK,CAAChC,QAAQ,CAAC;IAC5G;IAEA,IAAI,CAACI,4BAA4B,EAAE;EACrC;EAEA0C,eAAeA,CAAA;IACb,IAAI,CAAC3E,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;IAEhD,IAAI,CAACN,eAAe,CAACkF,OAAO,CAACf,KAAK,IAAG;MACnCA,KAAK,CAAC/B,QAAQ,GAAG,IAAI,CAAC9B,iBAAiB;MACvC,IAAI,IAAI,CAACA,iBAAiB,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC7F,yBAAyB,CAAC4H,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgC,KAAK,CAAChC,QAAQ,CAAC,EAAE;UAC5E,IAAI,CAAC1H,yBAAyB,CAACsK,IAAI,CAAC;YAAE,GAAGZ;UAAK,CAAE,CAAC;QACnD;MACF,CAAC,MAAM;QACL,IAAI,CAAC1J,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACuK,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgC,KAAK,CAAChC,QAAQ,CAAC;MAC5G;IACF,CAAC,CAAC;EACJ;EAEAgD,mBAAmBA,CAAChB,KAAwB;IAC1C,IAAI,CAAC1J,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACuK,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgC,KAAK,CAAChC,QAAQ,CAAC;IAE1G,MAAMiD,cAAc,GAAG,IAAI,CAACpF,eAAe,CAAC2C,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKgC,KAAK,CAAChC,QAAQ,CAAC;IACpF,IAAIiD,cAAc,EAAE;MAClBA,cAAc,CAAChD,QAAQ,GAAG,KAAK;IACjC;IAEA,IAAI,CAACG,4BAA4B,EAAE;EACrC;EAEAA,4BAA4BA,CAAA;IAC1B,IAAI,CAACjC,iBAAiB,GAAG,IAAI,CAACN,eAAe,CAACrF,MAAM,GAAG,CAAC,IACtD,IAAI,CAACqF,eAAe,CAACqF,KAAK,CAAClB,KAAK,IAAIA,KAAK,CAAC/B,QAAQ,CAAC;EACvD;EAEA;EACAkD,mBAAmBA,CAAC1D,IAAsB;IACxCA,IAAI,CAACQ,QAAQ,GAAG,CAACR,IAAI,CAACQ,QAAQ;IAE9B,IAAIR,IAAI,CAACQ,QAAQ,EAAE;MACjB,IAAI,CAAC,IAAI,CAACxH,wBAAwB,CAACyH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE;QAC1E;QACA,MAAMoD,OAAO,GAAG;UACd,GAAG3D,IAAI;UACPtG,UAAU,EAAE,CAAC;UAAE;UACfC,KAAK,EAAE,EAAE,CAAC;SACX;QACD,IAAI,CAACX,wBAAwB,CAACmK,IAAI,CAACQ,OAAO,CAAC;MAC7C;IACF,CAAC,MAAM;MACL,IAAI,CAAC3K,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACoK,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IACzG;IAEA,IAAI,CAACS,2BAA2B,EAAE;EACpC;EAEA4C,cAAcA,CAAA;IACZ,IAAI,CAAC3E,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAE9C,IAAI,CAACN,yBAAyB,CAAC2E,OAAO,CAACtD,IAAI,IAAG;MAC5CA,IAAI,CAACQ,QAAQ,GAAG,IAAI,CAACvB,gBAAgB;MACrC,IAAI,IAAI,CAACA,gBAAgB,EAAE;QACzB,IAAI,CAAC,IAAI,CAACjG,wBAAwB,CAACyH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC,EAAE;UAC1E;UACA,MAAMoD,OAAO,GAAG;YACd,GAAG3D,IAAI;YACPtG,UAAU,EAAE,CAAC;YAAE;YACfC,KAAK,EAAE,EAAE,CAAC;WACX;UACD,IAAI,CAACX,wBAAwB,CAACmK,IAAI,CAACQ,OAAO,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,CAAC3K,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACoK,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;MACzG;IACF,CAAC,CAAC;EACJ;EAEAlG,kBAAkBA,CAAC2F,IAAsB;IACvC,IAAI,CAAChH,wBAAwB,GAAG,IAAI,CAACA,wBAAwB,CAACoK,MAAM,CAAC1C,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAEvG,MAAMsD,aAAa,GAAG,IAAI,CAAClF,yBAAyB,CAACoC,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAIsD,aAAa,EAAE;MACjBA,aAAa,CAACrD,QAAQ,GAAG,KAAK;IAChC;IAEA,IAAI,CAACQ,2BAA2B,EAAE;EACpC;EAEAA,2BAA2BA,CAAA;IACzB,IAAI,CAAC/B,gBAAgB,GAAG,IAAI,CAACN,yBAAyB,CAAC5F,MAAM,GAAG,CAAC,IAC/D,IAAI,CAAC4F,yBAAyB,CAAC8E,KAAK,CAACzD,IAAI,IAAIA,IAAI,CAACQ,QAAQ,CAAC;EAC/D;EAEA;EACAsD,eAAeA,CAAC9D,IAAsB,EAAE+D,KAAa;IACnD;IACA,IAAI1B,KAAK,CAAC0B,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7BA,KAAK,GAAG,CAAC;IACX;IAEA/D,IAAI,CAACtG,UAAU,GAAGqK,KAAK;IACvB;IACA,MAAMF,aAAa,GAAG,IAAI,CAAClF,yBAAyB,CAACoC,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAIsD,aAAa,EAAE;MACjBA,aAAa,CAACnK,UAAU,GAAGqK,KAAK;IAClC;EACF;EAEA;EACA/J,aAAaA,CAACgG,IAAsB,EAAEgE,KAAU;IAC9C;IACA,MAAMC,YAAY,GAAG,OAAOD,KAAK,KAAK,QAAQ,GAAGE,UAAU,CAACF,KAAK,CAAC,GAAGA,KAAK;IAC1EhE,IAAI,CAACtG,UAAU,GAAG2I,KAAK,CAAC4B,YAAY,CAAC,GAAG,CAAC,GAAGA,YAAY;EAC1D;EAEAE,cAAcA,CAACnE,IAAsB,EAAEoE,IAAY;IACjD;IACAA,IAAI,GAAGA,IAAI,GAAGA,IAAI,CAAC1J,IAAI,EAAE,GAAG,EAAE;IAE9BsF,IAAI,CAACrG,KAAK,GAAGyK,IAAI;IACjB;IACA,MAAMP,aAAa,GAAG,IAAI,CAAClF,yBAAyB,CAACoC,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACH,QAAQ,KAAKP,IAAI,CAACO,QAAQ,CAAC;IAC5F,IAAIsD,aAAa,EAAE;MACjBA,aAAa,CAAClK,KAAK,GAAGyK,IAAI;IAC5B;EACF;EAEA;EACAC,sBAAsBA,CAACC,cAAiC;IACtD,IAAI,CAACzL,yBAAyB,GAAGyL,cAAc,CAACvE,GAAG,CAACwC,KAAK,KAAK;MAC5DhC,QAAQ,EAAEgC,KAAK,CAAChC,QAAQ;MACxB7H,KAAK,EAAE6J,KAAK,CAAC7J,KAAK;MAClBC,SAAS,EAAE4J,KAAK,CAAC5J,SAAS;MAC1B6H,QAAQ,EAAE;KACX,CAAC,CAAC;EACL;EAEA;EACA+D,qBAAqBA,CAACC,aAAgC;IACpD,IAAI,CAACxL,wBAAwB,GAAGwL,aAAa,CAACzE,GAAG,CAACC,IAAI,KAAK;MACzDO,QAAQ,EAAEP,IAAI,CAACO,QAAQ;MACvB7H,KAAK,EAAEsH,IAAI,CAACtH,KAAK;MACjBC,SAAS,EAAEqH,IAAI,CAACrH,SAAS;MACzB6H,QAAQ,EAAE,IAAI;MACd9G,UAAU,EAAE,CAAC;MAAE;MACfC,KAAK,EAAE,EAAE,CAAC;KACX,CAAC,CAAC;EACL;EAEA;EACAH,iBAAiBA,CAACgL,aAAgC;IAChD,IAAI,IAAI,CAACxM,cAAc,CAACV,aAAa,KAAKlC,gBAAgB,CAAC6C,aAAa,EAAE;MACxE,IAAI,CAACoM,sBAAsB,CAACG,aAAa,CAAC;IAC5C,CAAC,MAAM,IAAI,IAAI,CAACxM,cAAc,CAACV,aAAa,KAAKlC,gBAAgB,CAACoD,YAAY,EAAE;MAC9E,IAAI,CAAC+L,qBAAqB,CAACC,aAAa,CAAC;IAC3C;EACF;EAEA;EACA/J,WAAWA,CAACuF,IAAsB;IAChC;IACA,IAAIA,IAAI,CAACtG,UAAU,KAAK0I,SAAS,IAAIpC,IAAI,CAACtG,UAAU,KAAK,IAAI,IAAIsG,IAAI,CAACtG,UAAU,IAAI,CAAC,EAAE;MACrF,OAAO,KAAK;IACd;IAEA;IACA,IAAI2I,KAAK,CAACrC,IAAI,CAACtG,UAAU,CAAC,EAAE;MAC1B,OAAO,KAAK;IACd;IAEA;IACA,IAAI,CAACsG,IAAI,CAACrG,KAAK,IAAIqG,IAAI,CAACrG,KAAK,CAACe,IAAI,EAAE,KAAK,EAAE,IAAIsF,IAAI,CAACrG,KAAK,CAACe,IAAI,EAAE,KAAK,GAAG,EAAE;MACxE,OAAO,KAAK;IACd;IAEA;IACA,IAAIsF,IAAI,CAACrG,KAAK,CAACe,IAAI,EAAE,CAAC3B,MAAM,GAAG,EAAE,EAAE;MACjC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEA;EACA8B,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC7B,wBAAwB,CAACoK,MAAM,CAACpD,IAAI,IAAI,IAAI,CAACvF,WAAW,CAACuF,IAAI,CAAC,CAAC,CAACjH,MAAM;EACpF;;;uCA3qBWoE,iBAAiB,EAAA7H,EAAA,CAAAmP,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArP,EAAA,CAAAmP,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAvP,EAAA,CAAAmP,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAzP,EAAA,CAAAmP,iBAAA,CAAAK,EAAA,CAAAE,YAAA,GAAA1P,EAAA,CAAAmP,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAA5P,EAAA,CAAAmP,iBAAA,CAAAU,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;YAAjBjI,iBAAiB;MAAAkI,SAAA;MAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;UC/D5BlQ,EADF,CAAAC,cAAA,iBAA0B,qBACR;UACdD,EAAA,CAAAY,SAAA,qBAAiC;UACnCZ,EAAA,CAAAc,YAAA,EAAiB;UAIbd,EAHJ,CAAAC,cAAA,mBAAc,aAE6E,aAChD;UACrCD,EAAA,CAAAY,SAAA,WAA+E;UAE7EZ,EADF,CAAAC,cAAA,UAAK,WACmD;UACpDD,EAAA,CAAAa,MAAA,iUACF;UAGNb,EAHM,CAAAc,YAAA,EAAI,EACA,EACF,EACF;UAMAd,EAHN,CAAAC,cAAA,cAA8B,cACN,cACqC,iBACT;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAExDd,EADF,CAAAC,cAAA,yBAA6B,iBAE8B;UAAvDD,EAAA,CAAA4C,gBAAA,2BAAAwN,2DAAAtN,MAAA;YAAA9C,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAArQ,EAAA,CAAAgD,kBAAA,CAAAmN,GAAA,CAAAxH,aAAA,EAAA7F,MAAA,MAAAqN,GAAA,CAAAxH,aAAA,GAAA7F,MAAA;YAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;UAAA,EAA2B;UAAC9C,EAAA,CAAAE,UAAA,yBAAAoQ,yDAAA;YAAAtQ,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAA,OAAArQ,EAAA,CAAAU,WAAA,CAAeyP,GAAA,CAAA7E,QAAA,EAAU;UAAA,EAAC;UAG9DtL,EAJM,CAAAc,YAAA,EACyD,EAC3C,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACf;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAEhDd,EADF,CAAAC,cAAA,yBAA6B,qBAC2E;UAAzDD,EAAA,CAAA4C,gBAAA,2BAAA2N,+DAAAzN,MAAA;YAAA9C,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAArQ,EAAA,CAAAgD,kBAAA,CAAAmN,GAAA,CAAAvH,YAAA,EAAA9F,MAAA,MAAAqN,GAAA,CAAAvH,YAAA,GAAA9F,MAAA;YAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;UAAA,EAA0B;UAAC9C,EAAA,CAAAE,UAAA,4BAAAsQ,gEAAA;YAAAxQ,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAA,OAAArQ,EAAA,CAAAU,WAAA,CAAkByP,GAAA,CAAA7E,QAAA,EAAU;UAAA,EAAC;UACnGtL,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACrCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAIjCb,EAJiC,CAAAc,YAAA,EAAY,EAC3B,EACE,EACZ,EACF;UAIFd,EAFJ,CAAAC,cAAA,cAAsB,cACqC,iBACT;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAQ;UAExDd,EADF,CAAAC,cAAA,yBAA6B,qBAEK;UADqBD,EAAA,CAAA4C,gBAAA,2BAAA6N,+DAAA3N,MAAA;YAAA9C,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAArQ,EAAA,CAAAgD,kBAAA,CAAAmN,GAAA,CAAAtH,kBAAA,EAAA/F,MAAA,MAAAqN,GAAA,CAAAtH,kBAAA,GAAA/F,MAAA;YAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;UAAA,EAAgC;UACnF9C,EAAA,CAAAE,UAAA,4BAAAwQ,gEAAA;YAAA1Q,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAA,OAAArQ,EAAA,CAAAU,WAAA,CAAkByP,GAAA,CAAA7E,QAAA,EAAU;UAAA,EAAC;UAC7BtL,EAAA,CAAAC,cAAA,qBAA0B;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACxCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAY;UACvCd,EAAA,CAAAC,cAAA,qBAAuB;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAInCb,EAJmC,CAAAc,YAAA,EAAY,EAC7B,EACE,EACZ,EACF;UAENd,EAAA,CAAAY,SAAA,cAEM;UAKFZ,EAFJ,CAAAC,cAAA,eAAuB,eACoC,kBACmB;UAApBD,EAAA,CAAAE,UAAA,mBAAAyQ,oDAAA;YAAA3Q,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAA,OAAArQ,EAAA,CAAAU,WAAA,CAASyP,GAAA,CAAA5E,OAAA,EAAS;UAAA,EAAC;UACvEvL,EAAA,CAAAY,SAAA,aAAgC;UAAAZ,EAAA,CAAAa,MAAA,qBAClC;UAAAb,EAAA,CAAAc,YAAA,EAAS;UACTd,EAAA,CAAAC,cAAA,kBAA8D;UAArBD,EAAA,CAAAE,UAAA,mBAAA0Q,oDAAA;YAAA5Q,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAA,OAAArQ,EAAA,CAAAU,WAAA,CAASyP,GAAA,CAAA7E,QAAA,EAAU;UAAA,EAAC;UAC3DtL,EAAA,CAAAY,SAAA,aAAkC;UAAAZ,EAAA,CAAAa,MAAA,qBACpC;UAEJb,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGJd,EADF,CAAAC,cAAA,eAAuB,eAC+B;UAClDD,EAAA,CAAA2B,UAAA,KAAAkP,oCAAA,qBAAmG;UAKzG7Q,EAFI,CAAAc,YAAA,EAAM,EACF,EACF;UAOEd,EAJR,CAAAC,cAAA,eAAmC,iBACc,aACtC,UACD,cACoC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC7Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,gCAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC/Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,0BAAG;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC9Cd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAa,MAAA,oBAAE;UAE5Cb,EAF4C,CAAAc,YAAA,EAAK,EAC1C,EACC;UACRd,EAAA,CAAAC,cAAA,aAAO;UA2BLD,EA1BA,CAAA2B,UAAA,KAAAmP,gCAAA,mBAA0C,KAAAC,gCAAA,iBA0BJ;UAQ9C/Q,EAHM,CAAAc,YAAA,EAAQ,EACF,EACJ,EACO;UAEbd,EADF,CAAAC,cAAA,0BAAsD,0BAEf;UADrBD,EAAA,CAAA4C,gBAAA,wBAAAoO,iEAAAlO,MAAA;YAAA9C,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAArQ,EAAA,CAAAgD,kBAAA,CAAAmN,GAAA,CAAA3H,SAAA,EAAA1F,MAAA,MAAAqN,GAAA,CAAA3H,SAAA,GAAA1F,MAAA;YAAA,OAAA9C,EAAA,CAAAU,WAAA,CAAAoC,MAAA;UAAA,EAAoB;UAClC9C,EAAA,CAAAE,UAAA,wBAAA8Q,iEAAAlO,MAAA;YAAA9C,EAAA,CAAAI,aAAA,CAAAiQ,GAAA;YAAA,OAAArQ,EAAA,CAAAU,WAAA,CAAcyP,GAAA,CAAApE,WAAA,CAAAjJ,MAAA,CAAmB;UAAA,EAAC;UAGxC9C,EAFI,CAAAc,YAAA,EAAiB,EACF,EACT;UAmWVd,EAhWA,CAAA2B,UAAA,KAAAsP,yCAAA,kCAAAjR,EAAA,CAAAkR,sBAAA,CAAgD,KAAAC,yCAAA,kCAAAnR,EAAA,CAAAkR,sBAAA,CAgWM;;;UAldxClR,EAAA,CAAA8B,SAAA,IAA2B;UAA3B9B,EAAA,CAAAiD,gBAAA,YAAAkN,GAAA,CAAAxH,aAAA,CAA2B;UASgB3I,EAAA,CAAA8B,SAAA,GAA0B;UAA1B9B,EAAA,CAAAiD,gBAAA,YAAAkN,GAAA,CAAAvH,YAAA,CAA0B;UAC1D5I,EAAA,CAAA8B,SAAA,EAAc;UAAd9B,EAAA,CAAAmC,UAAA,eAAc;UACdnC,EAAA,CAAA8B,SAAA,GAAW;UAAX9B,EAAA,CAAAmC,UAAA,YAAW;UACXnC,EAAA,CAAA8B,SAAA,GAAW;UAAX9B,EAAA,CAAAmC,UAAA,YAAW;UAU6BnC,EAAA,CAAA8B,SAAA,GAAgC;UAAhC9B,EAAA,CAAAiD,gBAAA,YAAAkN,GAAA,CAAAtH,kBAAA,CAAgC;UAExE7I,EAAA,CAAA8B,SAAA,EAAc;UAAd9B,EAAA,CAAAmC,UAAA,eAAc;UACdnC,EAAA,CAAA8B,SAAA,GAAW;UAAX9B,EAAA,CAAAmC,UAAA,YAAW;UACXnC,EAAA,CAAA8B,SAAA,GAAW;UAAX9B,EAAA,CAAAmC,UAAA,YAAW;UAwBgBnC,EAAA,CAAA8B,SAAA,IAAc;UAAd9B,EAAA,CAAAmC,UAAA,SAAAgO,GAAA,CAAAiB,QAAA,CAAc;UAqB/BpR,EAAA,CAAA8B,SAAA,IAAe;UAAf9B,EAAA,CAAAmC,UAAA,YAAAgO,GAAA,CAAAzH,YAAA,CAAe;UA0BnC1I,EAAA,CAAA8B,SAAA,EAA+B;UAA/B9B,EAAA,CAAAmC,UAAA,SAAAgO,GAAA,CAAAzH,YAAA,CAAAjF,MAAA,OAA+B;UAU1BzD,EAAA,CAAA8B,SAAA,GAAoB;UAApB9B,EAAA,CAAAiD,gBAAA,SAAAkN,GAAA,CAAA3H,SAAA,CAAoB;UAAuBxI,EAAtB,CAAAmC,UAAA,aAAAgO,GAAA,CAAA5H,QAAA,CAAqB,mBAAA4H,GAAA,CAAA1H,YAAA,CAAgC;;;qBDzE1F/I,YAAY,EAAA2R,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,OAAA,EAAAF,EAAA,CAAAG,IAAA,EAAAH,EAAA,CAAAI,WAAA,EAAAJ,EAAA,CAAAK,QAAA,EACZ/R,YAAY,EAAAgS,EAAA,CAAAC,oBAAA,EAAAD,EAAA,CAAAE,mBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,kBAAA,EAAAJ,EAAA,CAAAK,YAAA,EAAAL,EAAA,CAAAM,OAAA,EAAA3C,EAAA,CAAA4C,eAAA,EAAA5C,EAAA,CAAA6C,mBAAA,EAAA7C,EAAA,CAAA8C,qBAAA,EAAA9C,EAAA,CAAA+C,qBAAA,EAAA/C,EAAA,CAAAgD,gBAAA,EAAAhD,EAAA,CAAAiD,iBAAA,EAAAjD,EAAA,CAAAkD,iBAAA,EAAAlD,EAAA,CAAAmD,oBAAA,EAAAnD,EAAA,CAAAoD,iBAAA,EAAApD,EAAA,CAAAqD,eAAA,EAAAC,EAAA,CAAAC,mBAAA,EAAAC,EAAA,CAAAC,mBAAA,EAEZnT,oBAAoB;MAAAoT,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}